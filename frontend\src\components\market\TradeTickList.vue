<template>
  <div class="trade-tick-list">
    <div class="tick-header">
      <h4>逐笔成交</h4>
      <div class="header-controls">
        <el-button size="small" @click="refreshTicks">
          <el-icon><Refresh /></el-icon>
        </el-button>
        <el-switch
          v-model="autoRefresh"
          size="small"
          active-text="自动刷新"
          inactive-text=""
        />
      </div>
    </div>

    <div class="tick-table-header">
      <div class="col-time">时间</div>
      <div class="col-price">价格</div>
      <div class="col-volume">数量</div>
      <div class="col-direction">方向</div>
    </div>

    <div class="tick-list" ref="tickListRef">
      <div
        v-for="(tick, index) in displayTicks"
        :key="`${tick.timestamp}-${index}`"
        class="tick-item"
        :class="getTickClass(tick)"
      >
        <div class="col-time">{{ formatTime(tick.timestamp) }}</div>
        <div class="col-price" :class="getPriceClass(tick.direction)">
          {{ tick.price }}
        </div>
        <div class="col-volume">{{ formatVolume(tick.volume) }}</div>
        <div class="col-direction">
          <span class="direction-indicator" :class="tick.direction">
            {{ getDirectionText(tick.direction) }}
          </span>
        </div>
      </div>
    </div>

    <div class="tick-summary">
      <div class="summary-item">
        <span class="label">总成交:</span>
        <span class="value">{{ totalTicks }}笔</span>
      </div>
      <div class="summary-item">
        <span class="label">总量:</span>
        <span class="value">{{ formatVolume(totalVolume) }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { Refresh } from '@element-plus/icons-vue'

const props = defineProps({
  symbol: {
    type: String,
    required: true
  },
  limit: {
    type: Number,
    default: 100
  }
})

const emit = defineEmits(['tick-update'])

// 响应式数据
const ticks = ref([])
const autoRefresh = ref(true)
const tickListRef = ref(null)
const refreshInterval = ref(null)

// 计算属性
const displayTicks = computed(() => {
  return ticks.value.slice(0, props.limit)
})

const totalTicks = computed(() => {
  return ticks.value.length
})

const totalVolume = computed(() => {
  return ticks.value.reduce((sum, tick) => sum + tick.volume, 0)
})

// 方法
const formatTime = (timestamp) => {
  const date = new Date(timestamp)
  return date.toLocaleTimeString('zh-CN', { 
    hour: '2-digit', 
    minute: '2-digit', 
    second: '2-digit' 
  })
}

const formatVolume = (volume) => {
  if (volume >= 10000) {
    return (volume / 10000).toFixed(1) + '万'
  }
  return volume.toString()
}

const getPriceClass = (direction) => {
  switch (direction) {
    case 'buy':
      return 'price-up'
    case 'sell':
      return 'price-down'
    default:
      return 'price-neutral'
  }
}

const getTickClass = (tick) => {
  return {
    'tick-new': tick.isNew,
    [`tick-${tick.direction}`]: true
  }
}

const getDirectionText = (direction) => {
  switch (direction) {
    case 'buy':
      return '买'
    case 'sell':
      return '卖'
    default:
      return '-'
  }
}

const refreshTicks = async () => {
  try {
    // 模拟API调用
    // const response = await fetch(`/api/v1/market/quotes/${props.symbol}/trades?limit=${props.limit}`)
    // const data = await response.json()
    
    // 生成模拟数据
    const newTicks = generateMockTicks(20)
    
    // 标记新数据
    newTicks.forEach(tick => {
      tick.isNew = true
    })
    
    // 合并数据
    ticks.value = [...newTicks, ...ticks.value].slice(0, props.limit * 2)
    
    // 清除新标记
    setTimeout(() => {
      ticks.value.forEach(tick => {
        tick.isNew = false
      })
    }, 1000)
    
    // 滚动到顶部
    await nextTick()
    if (tickListRef.value) {
      tickListRef.value.scrollTop = 0
    }
    
    emit('tick-update', newTicks)
  } catch (error) {
    console.error('获取逐笔成交数据失败:', error)
  }
}

const generateMockTicks = (count) => {
  const ticks = []
  const basePrice = 12.45
  const now = Date.now()
  
  for (let i = 0; i < count; i++) {
    const priceChange = (Math.random() - 0.5) * 0.1
    const price = (basePrice + priceChange).toFixed(2)
    const volume = Math.floor(Math.random() * 2000) + 100
    const direction = Math.random() > 0.5 ? 'buy' : 'sell'
    
    ticks.push({
      timestamp: now - i * Math.random() * 10000,
      price: parseFloat(price),
      volume,
      direction,
      amount: parseFloat(price) * volume
    })
  }
  
  return ticks.sort((a, b) => b.timestamp - a.timestamp)
}

const startAutoRefresh = () => {
  if (refreshInterval.value) {
    clearInterval(refreshInterval.value)
  }
  
  if (autoRefresh.value) {
    refreshInterval.value = setInterval(() => {
      refreshTicks()
    }, 2000)
  }
}

const stopAutoRefresh = () => {
  if (refreshInterval.value) {
    clearInterval(refreshInterval.value)
    refreshInterval.value = null
  }
}

// 监听器
watch(autoRefresh, (newValue) => {
  if (newValue) {
    startAutoRefresh()
  } else {
    stopAutoRefresh()
  }
})

watch(() => props.symbol, () => {
  ticks.value = []
  refreshTicks()
})

// 生命周期
onMounted(() => {
  refreshTicks()
  startAutoRefresh()
})

onUnmounted(() => {
  stopAutoRefresh()
})
</script>

<style scoped>
.trade-tick-list {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.tick-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #ebeef5;
}

.tick-header h4 {
  margin: 0;
  font-size: 14px;
  color: #333;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.tick-table-header {
  display: grid;
  grid-template-columns: 80px 1fr 80px 40px;
  gap: 8px;
  padding: 8px 16px;
  background: #f8f9fa;
  font-size: 12px;
  font-weight: 500;
  color: #666;
  border-bottom: 1px solid #ebeef5;
}

.tick-list {
  flex: 1;
  overflow-y: auto;
  font-family: 'Courier New', monospace;
}

.tick-item {
  display: grid;
  grid-template-columns: 80px 1fr 80px 40px;
  gap: 8px;
  padding: 6px 16px;
  font-size: 12px;
  border-bottom: 1px solid #f0f0f0;
  transition: all 0.3s;
}

.tick-item:hover {
  background: #f8f9fa;
}

.tick-item.tick-new {
  background: #fff7e6;
  animation: highlight 1s ease-out;
}

@keyframes highlight {
  0% {
    background: #fffbe6;
  }
  100% {
    background: #fff7e6;
  }
}

.col-time {
  color: #666;
}

.col-price {
  text-align: right;
  font-weight: 500;
}

.col-volume {
  text-align: right;
  color: #666;
}

.col-direction {
  text-align: center;
}

.direction-indicator {
  display: inline-block;
  width: 20px;
  height: 16px;
  line-height: 16px;
  text-align: center;
  border-radius: 2px;
  font-size: 10px;
  font-weight: bold;
}

.direction-indicator.buy {
  background: #f56c6c;
  color: white;
}

.direction-indicator.sell {
  background: #67c23a;
  color: white;
}

.direction-indicator.neutral {
  background: #909399;
  color: white;
}

.price-up {
  color: #f56c6c;
}

.price-down {
  color: #67c23a;
}

.price-neutral {
  color: #333;
}

.tick-summary {
  display: flex;
  justify-content: space-around;
  padding: 8px 16px;
  background: #f8f9fa;
  border-top: 1px solid #ebeef5;
  font-size: 12px;
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.summary-item .label {
  color: #666;
}

.summary-item .value {
  font-weight: 500;
  color: #333;
}
</style>
