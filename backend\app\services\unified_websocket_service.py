"""
统一的WebSocket服务
整合所有WebSocket功能，提供统一的接口和协议
"""

import asyncio
import json
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, Set, Optional, Any, List, Union
from collections import defaultdict, deque
from dataclasses import dataclass, asdict
from enum import Enum

from fastapi import WebSocket, WebSocketDisconnect
from fastapi.websockets import WebSocketState

logger = logging.getLogger(__name__)


class MessageType(str, Enum):
    """WebSocket消息类型"""
    # 连接管理
    CONNECT = "connect"
    DISCONNECT = "disconnect"
    HEARTBEAT_PING = "ping"
    HEARTBEAT_PONG = "pong"
    
    # 订阅管理
    SUBSCRIBE = "subscribe"
    UNSUBSCRIBE = "unsubscribe"
    SUBSCRIPTION_ACK = "subscription_ack"
    
    # 数据推送
    MARKET_DATA = "market_data"
    TRADING_UPDATE = "trading_update"
    PORTFOLIO_UPDATE = "portfolio_update"
    STRATEGY_UPDATE = "strategy_update"
    SYSTEM_NOTIFICATION = "system_notification"
    
    # 错误处理
    ERROR = "error"
    WARNING = "warning"
    
    # 状态信息
    STATUS = "status"
    CONFIG_UPDATE = "config_update"


class SubscriptionTopic(str, Enum):
    """订阅主题"""
    MARKET_DATA = "market_data"
    TRADING_UPDATES = "trading_updates" 
    PORTFOLIO = "portfolio"
    STRATEGIES = "strategies"
    SYSTEM_ALERTS = "system_alerts"
    ORDER_BOOK = "order_book"
    TICK_DATA = "tick_data"


@dataclass
class ConnectionInfo:
    """连接信息"""
    client_id: str
    user_id: Optional[str] = None
    client_ip: str = "unknown"
    connected_at: datetime = None
    last_activity: datetime = None
    messages_sent: int = 0
    messages_received: int = 0
    errors: int = 0
    subscriptions: Set[str] = None
    
    def __post_init__(self):
        if self.connected_at is None:
            self.connected_at = datetime.now()
        if self.last_activity is None:
            self.last_activity = datetime.now()
        if self.subscriptions is None:
            self.subscriptions = set()


@dataclass
class WebSocketMessage:
    """标准化WebSocket消息"""
    type: MessageType
    data: Any = None
    topic: Optional[str] = None
    timestamp: datetime = None
    client_id: Optional[str] = None
    error_code: Optional[str] = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        result = {
            "type": self.type.value,
            "timestamp": self.timestamp.isoformat()
        }
        
        if self.data is not None:
            result["data"] = self.data
        if self.topic is not None:
            result["topic"] = self.topic
        if self.client_id is not None:
            result["client_id"] = self.client_id
        if self.error_code is not None:
            result["error_code"] = self.error_code
            
        return result
    
    def to_json(self) -> str:
        """转换为JSON字符串"""
        return json.dumps(self.to_dict(), ensure_ascii=False, default=str)


class RateLimiter:
    """简单的速率限制器"""
    
    def __init__(self, max_requests: int = 100, window_seconds: int = 60):
        self.max_requests = max_requests
        self.window_seconds = window_seconds
        self.requests: Dict[str, deque] = defaultdict(deque)
    
    def is_allowed(self, client_id: str) -> bool:
        """检查是否允许请求"""
        now = time.time()
        window_start = now - self.window_seconds
        
        # 清理过期记录
        client_requests = self.requests[client_id]
        while client_requests and client_requests[0] < window_start:
            client_requests.popleft()
        
        # 检查是否超过限制
        if len(client_requests) >= self.max_requests:
            return False
        
        # 记录新请求
        client_requests.append(now)
        return True


class DataProcessor:
    """数据处理器"""
    
    def __init__(self):
        self.data_cache: Dict[str, Dict[str, Any]] = {}
        self.last_update: Dict[str, float] = {}
    
    async def process_market_data(self, symbol: str, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """处理市场数据"""
        try:
            # 数据验证和清理
            processed_data = {
                "symbol": symbol,
                "price": float(data.get("price", 0)),
                "change": float(data.get("change", 0)),
                "change_pct": float(data.get("change_pct", 0)),
                "volume": int(data.get("volume", 0)),
                "high": float(data.get("high", 0)),
                "low": float(data.get("low", 0)),
                "open": float(data.get("open", 0)),
                "timestamp": data.get("timestamp", datetime.now().isoformat())
            }
            
            # 检查是否有显著变化
            if self._has_significant_change(symbol, processed_data):
                self.data_cache[symbol] = processed_data
                self.last_update[symbol] = time.time()
                return processed_data
            
            return None
            
        except Exception as e:
            logger.error(f"处理市场数据失败 {symbol}: {e}")
            return None
    
    def _has_significant_change(self, symbol: str, new_data: Dict[str, Any]) -> bool:
        """检查是否有显著变化"""
        if symbol not in self.data_cache:
            return True
        
        old_data = self.data_cache[symbol]
        
        # 价格变动超过0.05%
        old_price = old_data.get("price", 0)
        new_price = new_data.get("price", 0)
        
        if old_price > 0:
            price_change = abs(new_price - old_price) / old_price
            if price_change > 0.0005:
                return True
        
        # 成交量变化
        if old_data.get("volume", 0) != new_data.get("volume", 0):
            return True
        
        # 心跳更新（超过1秒无更新）
        last_update = self.last_update.get(symbol, 0)
        if time.time() - last_update > 1.0:
            return True
        
        return False


class UnifiedWebSocketManager:
    """统一WebSocket管理器"""
    
    def __init__(self):
        # 连接管理
        self.connections: Dict[str, WebSocket] = {}
        self.connection_info: Dict[str, ConnectionInfo] = {}
        
        # 订阅管理
        self.topic_subscriptions: Dict[str, Set[str]] = defaultdict(set)  # topic -> client_ids
        self.client_subscriptions: Dict[str, Set[str]] = defaultdict(set)  # client_id -> topics
        
        # 限制和控制
        self.rate_limiter = RateLimiter(max_requests=100, window_seconds=60)
        self.data_processor = DataProcessor()
        
        # 配置
        self.max_connections = 1000
        self.max_connections_per_ip = 10
        self.max_subscriptions_per_client = 50
        self.heartbeat_interval = 30
        self.heartbeat_timeout = 60
        
        # 运行状态
        self.is_running = False
        self.background_tasks: Set[asyncio.Task] = set()
        
        # 统计信息
        self.stats = {
            "total_connections": 0,
            "messages_sent": 0,
            "messages_received": 0,
            "errors": 0,
            "start_time": datetime.now()
        }
        
        # IP连接计数
        self.ip_connections: Dict[str, Set[str]] = defaultdict(set)
    
    async def connect(self, websocket: WebSocket, client_id: str, 
                     user_id: Optional[str] = None, client_ip: str = "unknown") -> bool:
        """建立WebSocket连接"""
        try:
            # 检查连接限制
            if len(self.connections) >= self.max_connections:
                await websocket.close(code=1008, reason="服务器连接数已满")
                return False
            
            if len(self.ip_connections[client_ip]) >= self.max_connections_per_ip:
                await websocket.close(code=1008, reason="IP连接数超限")
                return False
            
            if client_id in self.connections:
                # 关闭旧连接
                await self.disconnect(client_id, code=1000, reason="新连接替换")
            
            # 接受连接
            await websocket.accept()
            
            # 存储连接信息
            self.connections[client_id] = websocket
            self.connection_info[client_id] = ConnectionInfo(
                client_id=client_id,
                user_id=user_id,
                client_ip=client_ip
            )
            self.ip_connections[client_ip].add(client_id)
            
            # 发送连接确认
            welcome_message = WebSocketMessage(
                type=MessageType.CONNECT,
                data={
                    "client_id": client_id,
                    "config": {
                        "heartbeat_interval": self.heartbeat_interval,
                        "max_subscriptions": self.max_subscriptions_per_client,
                        "supported_topics": [topic.value for topic in SubscriptionTopic]
                    }
                }
            )
            
            await self._send_to_client(client_id, welcome_message)
            
            # 更新统计
            self.stats["total_connections"] += 1
            
            logger.info(f"WebSocket客户端 {client_id} 连接成功，总连接数: {len(self.connections)}")
            return True
            
        except Exception as e:
            logger.error(f"WebSocket连接失败 {client_id}: {e}")
            return False
    
    async def disconnect(self, client_id: str, code: int = 1000, reason: str = "正常关闭"):
        """断开WebSocket连接"""
        if client_id not in self.connections:
            return
        
        websocket = self.connections[client_id]
        
        try:
            # 发送断开消息
            disconnect_message = WebSocketMessage(
                type=MessageType.DISCONNECT,
                data={"code": code, "reason": reason}
            )
            await self._send_to_client(client_id, disconnect_message)
            
            # 关闭连接
            if websocket.client_state == WebSocketState.CONNECTED:
                await websocket.close(code=code, reason=reason)
                
        except Exception as e:
            logger.debug(f"关闭WebSocket连接时出错 {client_id}: {e}")
        
        finally:
            # 清理连接数据
            self._cleanup_client(client_id)
            logger.info(f"WebSocket客户端 {client_id} 已断开，剩余连接数: {len(self.connections)}")
    
    def _cleanup_client(self, client_id: str):
        """清理客户端数据"""
        # 移除连接
        self.connections.pop(client_id, None)
        
        # 清理连接信息
        conn_info = self.connection_info.pop(client_id, None)
        if conn_info:
            self.ip_connections[conn_info.client_ip].discard(client_id)
            if not self.ip_connections[conn_info.client_ip]:
                del self.ip_connections[conn_info.client_ip]
        
        # 清理订阅
        topics = self.client_subscriptions.pop(client_id, set()).copy()
        for topic in topics:
            self.topic_subscriptions[topic].discard(client_id)
            if not self.topic_subscriptions[topic]:
                del self.topic_subscriptions[topic]
    
    async def subscribe(self, client_id: str, topic: str, filters: Optional[Dict[str, Any]] = None) -> bool:
        """订阅主题"""
        if client_id not in self.connections:
            return False
        
        # 检查订阅数量限制
        current_subs = len(self.client_subscriptions[client_id])
        if current_subs >= self.max_subscriptions_per_client:
            error_message = WebSocketMessage(
                type=MessageType.ERROR,
                data={
                    "message": f"订阅数量超过限制 ({self.max_subscriptions_per_client})",
                    "code": "SUBSCRIPTION_LIMIT_EXCEEDED"
                }
            )
            await self._send_to_client(client_id, error_message)
            return False
        
        # 添加订阅
        self.topic_subscriptions[topic].add(client_id)
        self.client_subscriptions[client_id].add(topic)
        
        # 存储过滤器（如果有）
        if filters:
            # TODO: 实现过滤器逻辑
            pass
        
        # 发送订阅确认
        ack_message = WebSocketMessage(
            type=MessageType.SUBSCRIPTION_ACK,
            topic=topic,
            data={"subscribed": True, "filters": filters}
        )
        await self._send_to_client(client_id, ack_message)
        
        logger.debug(f"客户端 {client_id} 订阅主题 {topic}")
        return True
    
    async def unsubscribe(self, client_id: str, topic: str) -> bool:
        """取消订阅"""
        if client_id not in self.connections:
            return False
        
        # 移除订阅
        self.topic_subscriptions[topic].discard(client_id)
        if not self.topic_subscriptions[topic]:
            del self.topic_subscriptions[topic]
        
        self.client_subscriptions[client_id].discard(topic)
        
        # 发送取消确认
        ack_message = WebSocketMessage(
            type=MessageType.SUBSCRIPTION_ACK,
            topic=topic,
            data={"subscribed": False}
        )
        await self._send_to_client(client_id, ack_message)
        
        logger.debug(f"客户端 {client_id} 取消订阅主题 {topic}")
        return True
    
    async def broadcast_to_topic(self, topic: str, message: WebSocketMessage):
        """向主题订阅者广播消息"""
        if topic not in self.topic_subscriptions:
            return
        
        message.topic = topic
        disconnected_clients = []
        
        for client_id in self.topic_subscriptions[topic].copy():
            success = await self._send_to_client(client_id, message)
            if not success:
                disconnected_clients.append(client_id)
        
        # 清理断开的客户端
        for client_id in disconnected_clients:
            await self.disconnect(client_id, 1002, "发送错误")
    
    async def send_to_client(self, client_id: str, message: WebSocketMessage) -> bool:
        """向指定客户端发送消息"""
        return await self._send_to_client(client_id, message)
    
    async def _send_to_client(self, client_id: str, message: WebSocketMessage) -> bool:
        """内部发送消息方法"""
        if client_id not in self.connections:
            return False
        
        websocket = self.connections[client_id]
        if websocket.client_state != WebSocketState.CONNECTED:
            return False
        
        try:
            await websocket.send_text(message.to_json())
            
            # 更新统计
            self.stats["messages_sent"] += 1
            conn_info = self.connection_info.get(client_id)
            if conn_info:
                conn_info.messages_sent += 1
                conn_info.last_activity = datetime.now()
            
            return True
            
        except Exception as e:
            logger.error(f"发送消息给客户端 {client_id} 失败: {e}")
            
            # 更新错误统计
            self.stats["errors"] += 1
            conn_info = self.connection_info.get(client_id)
            if conn_info:
                conn_info.errors += 1
            
            return False
    
    async def handle_message(self, client_id: str, raw_message: str):
        """处理客户端消息"""
        if client_id not in self.connections:
            return
        
        # 速率限制检查
        if not self.rate_limiter.is_allowed(client_id):
            error_message = WebSocketMessage(
                type=MessageType.ERROR,
                data={"message": "消息发送过于频繁", "code": "RATE_LIMIT_EXCEEDED"}
            )
            await self._send_to_client(client_id, error_message)
            return
        
        try:
            # 解析消息
            message_data = json.loads(raw_message)
            message_type = message_data.get("type")
            
            # 更新统计
            self.stats["messages_received"] += 1
            conn_info = self.connection_info.get(client_id)
            if conn_info:
                conn_info.messages_received += 1
                conn_info.last_activity = datetime.now()
            
            # 处理不同类型的消息
            if message_type == MessageType.HEARTBEAT_PING.value:
                await self._handle_ping(client_id)
                
            elif message_type == MessageType.SUBSCRIBE.value:
                topic = message_data.get("topic")
                filters = message_data.get("filters")
                if topic:
                    await self.subscribe(client_id, topic, filters)
                    
            elif message_type == MessageType.UNSUBSCRIBE.value:
                topic = message_data.get("topic")
                if topic:
                    await self.unsubscribe(client_id, topic)
                    
            else:
                error_message = WebSocketMessage(
                    type=MessageType.ERROR,
                    data={"message": f"未知消息类型: {message_type}", "code": "UNKNOWN_MESSAGE_TYPE"}
                )
                await self._send_to_client(client_id, error_message)
                
        except json.JSONDecodeError:
            error_message = WebSocketMessage(
                type=MessageType.ERROR,
                data={"message": "无效的JSON格式", "code": "INVALID_JSON"}
            )
            await self._send_to_client(client_id, error_message)
            
        except Exception as e:
            logger.error(f"处理客户端消息失败 {client_id}: {e}")
            error_message = WebSocketMessage(
                type=MessageType.ERROR,
                data={"message": "消息处理错误", "code": "MESSAGE_PROCESSING_ERROR"}
            )
            await self._send_to_client(client_id, error_message)
    
    async def _handle_ping(self, client_id: str):
        """处理心跳ping"""
        pong_message = WebSocketMessage(
            type=MessageType.HEARTBEAT_PONG,
            data={"timestamp": datetime.now().isoformat()}
        )
        await self._send_to_client(client_id, pong_message)
    
    async def push_market_data(self, symbol: str, data: Dict[str, Any]):
        """推送市场数据"""
        processed_data = await self.data_processor.process_market_data(symbol, data)
        if processed_data:
            message = WebSocketMessage(
                type=MessageType.MARKET_DATA,
                data=processed_data
            )
            await self.broadcast_to_topic(SubscriptionTopic.MARKET_DATA.value, message)
    
    async def push_trading_update(self, update_data: Dict[str, Any]):
        """推送交易更新"""
        message = WebSocketMessage(
            type=MessageType.TRADING_UPDATE,
            data=update_data
        )
        await self.broadcast_to_topic(SubscriptionTopic.TRADING_UPDATES.value, message)
    
    async def start(self):
        """启动WebSocket管理器"""
        if self.is_running:
            return
        
        self.is_running = True
        
        # 启动心跳检查任务
        heartbeat_task = asyncio.create_task(self._heartbeat_loop())
        self.background_tasks.add(heartbeat_task)
        heartbeat_task.add_done_callback(self.background_tasks.discard)
        
        logger.info("统一WebSocket管理器已启动")
    
    async def shutdown(self):
        """关闭WebSocket管理器"""
        self.is_running = False
        
        # 取消所有后台任务
        for task in self.background_tasks:
            task.cancel()
        
        # 等待任务完成
        await asyncio.gather(*self.background_tasks, return_exceptions=True)
        
        # 断开所有连接
        client_ids = list(self.connections.keys())
        for client_id in client_ids:
            await self.disconnect(client_id, 1001, "服务器关闭")
        
        logger.info("统一WebSocket管理器已关闭")
    
    async def _heartbeat_loop(self):
        """心跳检查循环"""
        while self.is_running:
            try:
                now = datetime.now()
                timeout_threshold = now - timedelta(seconds=self.heartbeat_timeout)
                
                # 找出超时的客户端
                timed_out = []
                for client_id, conn_info in self.connection_info.items():
                    if conn_info.last_activity < timeout_threshold:
                        timed_out.append(client_id)
                
                # 断开超时的连接
                for client_id in timed_out:
                    logger.warning(f"客户端 {client_id} 心跳超时")
                    await self.disconnect(client_id, 1001, "心跳超时")
                
                await asyncio.sleep(self.heartbeat_interval)
                
            except Exception as e:
                logger.error(f"心跳检查错误: {e}")
                await asyncio.sleep(5)
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        uptime = datetime.now() - self.stats["start_time"]
        
        return {
            "connections": {
                "active": len(self.connections),
                "total_created": self.stats["total_connections"],
                "by_ip": {ip: len(clients) for ip, clients in self.ip_connections.items()}
            },
            "subscriptions": {
                "topics": len(self.topic_subscriptions),
                "total_subscriptions": sum(len(clients) for clients in self.topic_subscriptions.values())
            },
            "messages": {
                "sent": self.stats["messages_sent"],
                "received": self.stats["messages_received"],
                "errors": self.stats["errors"]
            },
            "system": {
                "uptime_seconds": int(uptime.total_seconds()),
                "is_running": self.is_running,
                "background_tasks": len(self.background_tasks)
            }
        }


# 全局统一WebSocket管理器实例
unified_ws_manager = UnifiedWebSocketManager()