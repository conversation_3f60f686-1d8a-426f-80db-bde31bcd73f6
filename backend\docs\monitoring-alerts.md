# 市场数据服务监控告警配置

## 概述

本文档描述了市场数据服务的Prometheus指标和建议的告警规则。

## 可用指标

### HTTP API 指标

#### `quant_platform_market_requests_total`
- **类型**: Counter
- **描述**: 市场API请求总数
- **标签**: 
  - `endpoint`: API端点路径
  - `method`: HTTP方法
  - `status`: HTTP状态码

#### `quant_platform_market_request_duration_seconds`
- **类型**: Histogram
- **描述**: 市场API请求持续时间
- **标签**: 
  - `endpoint`: API端点路径
  - **method**: HTTP方法
- **桶**: [0.01, 0.05, 0.1, 0.25, 0.5, 1.0, 2.5, 5.0, 10.0]

### 数据源指标

#### `quant_platform_market_data_source_requests_total`
- **类型**: Counter
- **描述**: 数据源请求总数
- **标签**:
  - `source`: 数据源名称 (tushare, akshare, mock)
  - `success`: 请求是否成功 (true/false)
  - `symbol`: 股票代码

#### `quant_platform_market_data_latency`
- **类型**: Histogram
- **描述**: 数据源响应延迟
- **标签**:
  - `source`: 数据源名称
  - `symbol`: 股票代码

#### `quant_platform_market_data_connection_status`
- **类型**: Gauge
- **描述**: 数据源连接状态 (1=健康, 0=不健康)
- **标签**:
  - `source`: 数据源名称

### 缓存指标

#### `quant_platform_market_cache_ops_total`
- **类型**: Counter
- **描述**: 缓存操作总数
- **标签**:
  - `operation`: 操作类型 (get, set, delete)
  - `cache_type`: 缓存类型 (realtime, historical, etc.)
  - `result`: 操作结果 (hit, miss, success, error)

### WebSocket 指标

#### `quant_platform_market_ws_connections`
- **类型**: Gauge
- **描述**: WebSocket连接数
- **标签**:
  - `status`: 连接状态 (active, idle)

#### `quant_platform_market_ws_messages_total`
- **类型**: Counter
- **描述**: WebSocket消息总数
- **标签**:
  - `type`: 消息类型 (connection, disconnection, subscribe, data)
  - `direction`: 消息方向 (inbound, outbound)

## 建议的告警规则

### 高优先级告警

#### 数据源失败率过高
```yaml
- alert: MarketDataSourceHighFailureRate
  expr: |
    (
      rate(quant_platform_market_data_source_requests_total{success="false"}[5m]) /
      rate(quant_platform_market_data_source_requests_total[5m])
    ) > 0.1
  for: 2m
  labels:
    severity: critical
  annotations:
    summary: "数据源失败率过高"
    description: "数据源 {{ $labels.source }} 在过去5分钟内失败率超过10%"
```

#### API响应时间过长
```yaml
- alert: MarketAPIHighLatency
  expr: |
    histogram_quantile(0.95, 
      rate(quant_platform_market_request_duration_seconds_bucket[5m])
    ) > 2.0
  for: 3m
  labels:
    severity: warning
  annotations:
    summary: "API响应时间过长"
    description: "95%的API请求响应时间超过2秒"
```

#### 服务不可用
```yaml
- alert: MarketAPIDown
  expr: |
    up{job="market-data-api"} == 0
  for: 1m
  labels:
    severity: critical
  annotations:
    summary: "市场数据API服务不可用"
    description: "市场数据API服务已停止响应"
```

### 中等优先级告警

#### 缓存命中率低
```yaml
- alert: MarketCacheLowHitRate
  expr: |
    (
      rate(quant_platform_market_cache_ops_total{result="hit"}[10m]) /
      rate(quant_platform_market_cache_ops_total{operation="get"}[10m])
    ) < 0.3 and
    rate(quant_platform_market_requests_total[10m]) > 10
  for: 5m
  labels:
    severity: warning
  annotations:
    summary: "缓存命中率过低"
    description: "缓存命中率低于30%，且QPS大于10"
```

#### WebSocket连接数异常
```yaml
- alert: MarketWebSocketConnectionsHigh
  expr: |
    quant_platform_market_ws_connections{status="active"} > 100
  for: 5m
  labels:
    severity: warning
  annotations:
    summary: "WebSocket连接数过高"
    description: "活跃WebSocket连接数超过100个"
```

### 低优先级告警

#### 数据源切换
```yaml
- alert: MarketDataSourceSwitched
  expr: |
    changes(quant_platform_market_data_connection_status[10m]) > 0
  for: 0m
  labels:
    severity: info
  annotations:
    summary: "数据源状态变化"
    description: "数据源 {{ $labels.source }} 状态发生变化"
```

## 监控仪表板建议

### 关键指标面板

1. **服务健康状态**
   - 服务可用性
   - API请求成功率
   - 平均响应时间

2. **数据源监控**
   - 各数据源请求量
   - 数据源失败率
   - 数据源响应时间

3. **缓存性能**
   - 缓存命中率
   - 缓存操作量
   - 缓存大小

4. **WebSocket监控**
   - 连接数趋势
   - 消息吞吐量
   - 连接错误率

## 部署说明

1. **Prometheus配置**
   ```yaml
   scrape_configs:
     - job_name: 'market-data-api'
       static_configs:
         - targets: ['localhost:8002']
       metrics_path: '/metrics'
       scrape_interval: 15s
   ```

2. **Grafana仪表板**
   - 导入提供的仪表板模板
   - 配置数据源指向Prometheus
   - 设置告警通知渠道

3. **告警管理器**
   - 配置告警路由规则
   - 设置通知渠道（邮件、钉钉、微信等）
   - 配置告警抑制规则
