"""
统一的WebSocket API端点
提供标准化的WebSocket连接和数据推送服务
"""

import asyncio
import json
import logging
import uuid
from typing import Optional, Dict, Any

from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends, HTTPException, Query
from pydantic import BaseModel

from app.services.unified_websocket_service import unified_ws_manager, MessageType, SubscriptionTopic
from app.core.dependencies import get_current_user_optional
from app.core.auth import get_current_user
from app.db.models.user import User

logger = logging.getLogger(__name__)
router = APIRouter()


class SubscriptionRequest(BaseModel):
    """订阅请求模型"""
    topics: list[str]
    filters: Optional[Dict[str, Any]] = None


class WebSocketConfigRequest(BaseModel):
    """WebSocket配置请求模型"""
    max_connections: Optional[int] = None
    max_connections_per_ip: Optional[int] = None
    max_subscriptions_per_client: Optional[int] = None
    heartbeat_interval: Optional[int] = None
    heartbeat_timeout: Optional[int] = None


@router.websocket("/ws")
async def websocket_unified_endpoint(
    websocket: WebSocket,
    client_id: Optional[str] = Query(None, description="客户端ID"),
    user: Optional[User] = Depends(get_current_user_optional)
):
    """
    统一WebSocket端点
    支持多种数据类型的实时推送
    """
    # 生成客户端ID
    if not client_id:
        client_id = f"client_{uuid.uuid4().hex[:8]}"
    
    # 获取客户端IP
    client_ip = websocket.client.host if websocket.client else "unknown"
    user_id = user.id if user else None
    
    # 建立连接
    connection_success = await unified_ws_manager.connect(
        websocket=websocket,
        client_id=client_id,
        user_id=user_id,
        client_ip=client_ip
    )
    
    if not connection_success:
        logger.warning(f"WebSocket连接被拒绝: {client_id}")
        return
    
    try:
        while True:
            # 接收客户端消息
            try:
                raw_message = await websocket.receive_text()
                await unified_ws_manager.handle_message(client_id, raw_message)
                
            except WebSocketDisconnect:
                logger.info(f"WebSocket客户端 {client_id} 主动断开连接")
                break
                
            except Exception as e:
                logger.error(f"WebSocket消息处理错误 {client_id}: {e}")
                # 继续处理其他消息
                
    except Exception as e:
        logger.error(f"WebSocket连接错误 {client_id}: {e}")
        
    finally:
        # 清理连接
        await unified_ws_manager.disconnect(client_id)


@router.websocket("/ws/market")
async def websocket_market_endpoint(
    websocket: WebSocket,
    client_id: Optional[str] = Query(None, description="客户端ID"),
    symbols: Optional[str] = Query(None, description="订阅的股票代码，逗号分隔")
):
    """
    市场数据专用WebSocket端点
    自动订阅市场数据主题
    """
    if not client_id:
        client_id = f"market_{uuid.uuid4().hex[:8]}"
    
    client_ip = websocket.client.host if websocket.client else "unknown"
    
    # 建立连接
    connection_success = await unified_ws_manager.connect(
        websocket=websocket,
        client_id=client_id,
        client_ip=client_ip
    )
    
    if not connection_success:
        return
    
    try:
        # 自动订阅市场数据
        await unified_ws_manager.subscribe(client_id, SubscriptionTopic.MARKET_DATA.value)
        
        # 如果指定了股票代码，添加过滤器
        if symbols:
            symbol_list = [s.strip() for s in symbols.split(",") if s.strip()]
            # TODO: 实现股票代码过滤器
            logger.info(f"客户端 {client_id} 订阅股票: {symbol_list}")
        
        # 保持连接并处理消息
        while True:
            try:
                raw_message = await websocket.receive_text()
                await unified_ws_manager.handle_message(client_id, raw_message)
                
            except WebSocketDisconnect:
                break
                
    except Exception as e:
        logger.error(f"市场数据WebSocket错误 {client_id}: {e}")
        
    finally:
        await unified_ws_manager.disconnect(client_id)


@router.websocket("/ws/trading")
async def websocket_trading_endpoint(
    websocket: WebSocket,
    client_id: Optional[str] = Query(None, description="客户端ID"),
    user: User = Depends(get_current_user)  # 交易功能需要认证
):
    """
    交易数据专用WebSocket端点
    需要用户认证，提供交易更新和持仓信息
    """
    if not client_id:
        client_id = f"trading_{user.id}_{uuid.uuid4().hex[:8]}"
    
    client_ip = websocket.client.host if websocket.client else "unknown"
    
    # 建立连接
    connection_success = await unified_ws_manager.connect(
        websocket=websocket,
        client_id=client_id,
        user_id=user.id,
        client_ip=client_ip
    )
    
    if not connection_success:
        return
    
    try:
        # 自动订阅交易相关主题
        await unified_ws_manager.subscribe(client_id, SubscriptionTopic.TRADING_UPDATES.value)
        await unified_ws_manager.subscribe(client_id, SubscriptionTopic.PORTFOLIO.value)
        
        # 处理消息
        while True:
            try:
                raw_message = await websocket.receive_text()
                await unified_ws_manager.handle_message(client_id, raw_message)
                
            except WebSocketDisconnect:
                break
                
    except Exception as e:
        logger.error(f"交易WebSocket错误 {client_id}: {e}")
        
    finally:
        await unified_ws_manager.disconnect(client_id)


@router.get("/ws/status")
async def get_websocket_status():
    """获取WebSocket服务状态"""
    return unified_ws_manager.get_stats()


@router.get("/ws/connections")
async def get_active_connections(user: User = Depends(get_current_user)):
    """获取活跃连接信息（需要管理员权限）"""
    if not user.is_admin:
        raise HTTPException(status_code=403, detail="需要管理员权限")
    
    stats = unified_ws_manager.get_stats()
    
    # 获取详细连接信息
    connection_details = []
    for client_id, conn_info in unified_ws_manager.connection_info.items():
        connection_details.append({
            "client_id": client_id,
            "user_id": conn_info.user_id,
            "client_ip": conn_info.client_ip,
            "connected_at": conn_info.connected_at.isoformat(),
            "last_activity": conn_info.last_activity.isoformat(),
            "messages_sent": conn_info.messages_sent,
            "messages_received": conn_info.messages_received,
            "errors": conn_info.errors,
            "subscriptions": list(conn_info.subscriptions)
        })
    
    return {
        "summary": stats,
        "connections": connection_details
    }


@router.post("/ws/broadcast")
async def broadcast_message(
    message_data: Dict[str, Any],
    topic: Optional[str] = Query(None, description="广播到指定主题"),
    user: User = Depends(get_current_user)
):
    """
    广播消息到WebSocket客户端
    需要管理员权限
    """
    if not user.is_admin:
        raise HTTPException(status_code=403, detail="需要管理员权限")
    
    try:
        from app.services.unified_websocket_service import WebSocketMessage
        
        # 创建系统通知消息
        message = WebSocketMessage(
            type=MessageType.SYSTEM_NOTIFICATION,
            data=message_data
        )
        
        if topic:
            # 广播到指定主题
            await unified_ws_manager.broadcast_to_topic(topic, message)
            target = f"主题 '{topic}'"
        else:
            # 广播到所有连接
            for client_id in unified_ws_manager.connections:
                await unified_ws_manager.send_to_client(client_id, message)
            target = "所有客户端"
        
        return {
            "success": True,
            "message": f"消息已广播到{target}",
            "recipients": len(unified_ws_manager.topic_subscriptions.get(topic, [])) if topic else len(unified_ws_manager.connections)
        }
        
    except Exception as e:
        logger.error(f"广播消息失败: {e}")
        raise HTTPException(status_code=500, detail=f"广播失败: {str(e)}")


@router.post("/ws/config")
async def update_websocket_config(
    config: WebSocketConfigRequest,
    user: User = Depends(get_current_user)
):
    """
    更新WebSocket配置
    需要管理员权限
    """
    if not user.is_admin:
        raise HTTPException(status_code=403, detail="需要管理员权限")
    
    try:
        updated_fields = []
        
        if config.max_connections is not None:
            unified_ws_manager.max_connections = config.max_connections
            updated_fields.append(f"max_connections: {config.max_connections}")
        
        if config.max_connections_per_ip is not None:
            unified_ws_manager.max_connections_per_ip = config.max_connections_per_ip
            updated_fields.append(f"max_connections_per_ip: {config.max_connections_per_ip}")
        
        if config.max_subscriptions_per_client is not None:
            unified_ws_manager.max_subscriptions_per_client = config.max_subscriptions_per_client
            updated_fields.append(f"max_subscriptions_per_client: {config.max_subscriptions_per_client}")
        
        if config.heartbeat_interval is not None:
            unified_ws_manager.heartbeat_interval = config.heartbeat_interval
            updated_fields.append(f"heartbeat_interval: {config.heartbeat_interval}")
        
        if config.heartbeat_timeout is not None:
            unified_ws_manager.heartbeat_timeout = config.heartbeat_timeout
            updated_fields.append(f"heartbeat_timeout: {config.heartbeat_timeout}")
        
        logger.info(f"WebSocket配置更新: {', '.join(updated_fields)}")
        
        return {
            "success": True,
            "message": "配置更新成功",
            "updated_fields": updated_fields,
            "current_config": {
                "max_connections": unified_ws_manager.max_connections,
                "max_connections_per_ip": unified_ws_manager.max_connections_per_ip,
                "max_subscriptions_per_client": unified_ws_manager.max_subscriptions_per_client,
                "heartbeat_interval": unified_ws_manager.heartbeat_interval,
                "heartbeat_timeout": unified_ws_manager.heartbeat_timeout
            }
        }
        
    except Exception as e:
        logger.error(f"更新WebSocket配置失败: {e}")
        raise HTTPException(status_code=500, detail=f"配置更新失败: {str(e)}")


@router.get("/ws/topics")
async def get_available_topics():
    """获取可用的订阅主题"""
    return {
        "topics": [
            {
                "name": topic.value,
                "description": _get_topic_description(topic),
                "subscribers": len(unified_ws_manager.topic_subscriptions.get(topic.value, []))
            }
            for topic in SubscriptionTopic
        ]
    }


def _get_topic_description(topic: SubscriptionTopic) -> str:
    """获取主题描述"""
    descriptions = {
        SubscriptionTopic.MARKET_DATA: "实时行情数据",
        SubscriptionTopic.TRADING_UPDATES: "交易订单更新",
        SubscriptionTopic.PORTFOLIO: "投资组合变动",
        SubscriptionTopic.STRATEGIES: "策略运行状态",
        SubscriptionTopic.SYSTEM_ALERTS: "系统通知和警告",
        SubscriptionTopic.ORDER_BOOK: "订单簿数据",
        SubscriptionTopic.TICK_DATA: "逐笔成交数据"
    }
    return descriptions.get(topic, "未知主题")


# 在路由器初始化时启动WebSocket管理器
@router.on_event("startup")
async def startup_websocket_manager():
    """启动时初始化WebSocket管理器"""
    await unified_ws_manager.start()
    logger.info("统一WebSocket服务已启动")


@router.on_event("shutdown") 
async def shutdown_websocket_manager():
    """关闭时清理WebSocket管理器"""
    await unified_ws_manager.shutdown()
    logger.info("统一WebSocket服务已关闭")


logger.info(f"统一WebSocket路由器已注册 {len(router.routes)} 个路由")