"""
缓存优化配置
提供针对不同数据类型的缓存策略和配置
"""

from datetime import timedelta
from enum import Enum
from typing import Dict, Any, List, Optional
from dataclasses import dataclass

from app.core.config import get_settings

settings = get_settings()


class DataType(str, Enum):
    """数据类型分类"""
    # 实时数据（高频变化）
    REALTIME_QUOTE = "realtime_quote"        # 实时行情
    TICK_DATA = "tick_data"                  # 逐笔数据
    ORDER_BOOK = "order_book"                # 订单簿
    
    # 行情数据（中频变化）
    MINUTE_KLINE = "minute_kline"            # 分钟K线
    DAILY_KLINE = "daily_kline"              # 日K线
    TECHNICAL_INDICATORS = "tech_indicators"  # 技术指标
    
    # 基础数据（低频变化）
    STOCK_INFO = "stock_info"                # 股票基本信息
    COMPANY_INFO = "company_info"            # 公司信息
    INDUSTRY_INFO = "industry_info"          # 行业信息
    
    # 用户数据
    USER_PROFILE = "user_profile"            # 用户资料
    USER_PORTFOLIO = "user_portfolio"        # 用户持仓
    USER_STRATEGY = "user_strategy"          # 用户策略
    
    # 系统数据
    SYSTEM_CONFIG = "system_config"          # 系统配置
    MARKET_STATUS = "market_status"          # 市场状态
    TRADING_CALENDAR = "trading_calendar"    # 交易日历


@dataclass
class CacheStrategy:
    """缓存策略配置"""
    ttl: int                           # 生存时间（秒）
    max_size: Optional[int] = None     # 最大条目数
    refresh_threshold: float = 0.8      # 刷新阈值（TTL的百分比）
    enable_compression: bool = False    # 是否启用压缩
    enable_encryption: bool = False     # 是否启用加密
    prefetch: bool = False             # 是否预取
    write_back_delay: int = 0          # 写回延迟（秒）
    tags: List[str] = None            # 缓存标签


class OptimizedCacheConfig:
    """优化的缓存配置"""
    
    # 缓存策略映射
    STRATEGIES: Dict[DataType, CacheStrategy] = {
        # 实时数据 - 短TTL，高刷新频率
        DataType.REALTIME_QUOTE: CacheStrategy(
            ttl=30,
            refresh_threshold=0.5,
            enable_compression=True,
            prefetch=True,
            tags=["realtime", "quote"]
        ),
        
        DataType.TICK_DATA: CacheStrategy(
            ttl=60,
            max_size=10000,
            enable_compression=True,
            tags=["realtime", "tick"]
        ),
        
        DataType.ORDER_BOOK: CacheStrategy(
            ttl=10,
            refresh_threshold=0.3,
            enable_compression=True,
            tags=["realtime", "orderbook"]
        ),
        
        # 行情数据 - 中等TTL
        DataType.MINUTE_KLINE: CacheStrategy(
            ttl=300,  # 5分钟
            enable_compression=True,
            prefetch=True,
            tags=["kline", "minute"]
        ),
        
        DataType.DAILY_KLINE: CacheStrategy(
            ttl=3600,  # 1小时
            enable_compression=True,
            prefetch=True,
            tags=["kline", "daily"]
        ),
        
        DataType.TECHNICAL_INDICATORS: CacheStrategy(
            ttl=900,  # 15分钟
            enable_compression=True,
            write_back_delay=60,
            tags=["indicator", "technical"]
        ),
        
        # 基础数据 - 长TTL
        DataType.STOCK_INFO: CacheStrategy(
            ttl=86400,  # 1天
            refresh_threshold=0.9,
            enable_compression=True,
            prefetch=True,
            tags=["stock", "info"]
        ),
        
        DataType.COMPANY_INFO: CacheStrategy(
            ttl=604800,  # 1周
            refresh_threshold=0.95,
            enable_compression=True,
            tags=["company", "info"]
        ),
        
        DataType.INDUSTRY_INFO: CacheStrategy(
            ttl=86400,  # 1天
            refresh_threshold=0.9,
            tags=["industry", "info"]
        ),
        
        # 用户数据 - 根据敏感性配置
        DataType.USER_PROFILE: CacheStrategy(
            ttl=7200,  # 2小时
            enable_encryption=True,
            tags=["user", "profile"]
        ),
        
        DataType.USER_PORTFOLIO: CacheStrategy(
            ttl=300,  # 5分钟
            enable_encryption=True,
            refresh_threshold=0.7,
            tags=["user", "portfolio"]
        ),
        
        DataType.USER_STRATEGY: CacheStrategy(
            ttl=3600,  # 1小时
            enable_encryption=True,
            tags=["user", "strategy"]
        ),
        
        # 系统数据 - 根据变化频率配置
        DataType.SYSTEM_CONFIG: CacheStrategy(
            ttl=3600,  # 1小时
            refresh_threshold=0.95,
            prefetch=True,
            tags=["system", "config"]
        ),
        
        DataType.MARKET_STATUS: CacheStrategy(
            ttl=60,  # 1分钟
            refresh_threshold=0.5,
            prefetch=True,
            tags=["market", "status"]
        ),
        
        DataType.TRADING_CALENDAR: CacheStrategy(
            ttl=86400,  # 1天
            refresh_threshold=0.95,
            prefetch=True,
            tags=["trading", "calendar"]
        )
    }
    
    # 缓存键模式
    KEY_PATTERNS: Dict[DataType, str] = {
        DataType.REALTIME_QUOTE: "rt:quote:{symbol}",
        DataType.TICK_DATA: "tick:{symbol}:{date}",
        DataType.ORDER_BOOK: "orderbook:{symbol}",
        DataType.MINUTE_KLINE: "kline:min:{symbol}:{period}",
        DataType.DAILY_KLINE: "kline:day:{symbol}:{period}",
        DataType.TECHNICAL_INDICATORS: "indicator:{symbol}:{type}:{period}",
        DataType.STOCK_INFO: "stock:info:{symbol}",
        DataType.COMPANY_INFO: "company:{symbol}",
        DataType.INDUSTRY_INFO: "industry:{code}",
        DataType.USER_PROFILE: "user:profile:{user_id}",
        DataType.USER_PORTFOLIO: "user:portfolio:{user_id}",
        DataType.USER_STRATEGY: "user:strategy:{user_id}:{strategy_id}",
        DataType.SYSTEM_CONFIG: "system:config:{key}",
        DataType.MARKET_STATUS: "market:status:{market}",
        DataType.TRADING_CALENDAR: "calendar:{year}:{month}"
    }
    
    # 预热配置
    WARMUP_CONFIG = {
        "enabled": True,
        "on_startup": True,
        "patterns": [
            "stock:info:*",
            "system:config:*",
            "market:status:*",
            "trading:calendar:*"
        ],
        "batch_size": 100,
        "delay_between_batches": 0.1
    }
    
    # 清理配置
    CLEANUP_CONFIG = {
        "enabled": True,
        "interval": 300,  # 5分钟
        "expired_cleanup": True,
        "memory_threshold": 0.85,  # 内存使用超过85%时清理
        "lru_cleanup_ratio": 0.2,  # 清理20%的LRU数据
        "tag_based_cleanup": True
    }
    
    # 监控配置
    MONITORING_CONFIG = {
        "enabled": True,
        "stats_interval": 300,  # 5分钟
        "alert_thresholds": {
            "hit_rate_min": 0.7,      # 命中率低于70%告警
            "memory_usage_max": 0.9,   # 内存使用超过90%告警
            "error_rate_max": 0.05     # 错误率超过5%告警
        },
        "performance_tracking": True
    }
    
    @classmethod
    def get_strategy(cls, data_type: DataType) -> CacheStrategy:
        """获取数据类型对应的缓存策略"""
        return cls.STRATEGIES.get(data_type, CacheStrategy(ttl=3600))
    
    @classmethod
    def get_key_pattern(cls, data_type: DataType) -> str:
        """获取数据类型对应的键模式"""
        return cls.KEY_PATTERNS.get(data_type, "{data_type}:{key}")
    
    @classmethod
    def format_key(cls, data_type: DataType, **kwargs) -> str:
        """格式化缓存键"""
        pattern = cls.get_key_pattern(data_type)
        try:
            return pattern.format(**kwargs)
        except KeyError as e:
            raise ValueError(f"Missing required parameter for key pattern: {e}")
    
    @classmethod
    def should_use_cache(cls, data_type: DataType, data_size: int = 0) -> bool:
        """判断是否应该使用缓存"""
        strategy = cls.get_strategy(data_type)
        
        # 检查数据大小限制
        if data_size > 10 * 1024 * 1024:  # 超过10MB不缓存
            return False
        
        # 检查是否有TTL配置
        if strategy.ttl <= 0:
            return False
        
        return True
    
    @classmethod
    def get_cache_tags(cls, data_type: DataType, **kwargs) -> List[str]:
        """获取缓存标签"""
        strategy = cls.get_strategy(data_type)
        tags = strategy.tags.copy() if strategy.tags else []
        
        # 添加动态标签
        if "symbol" in kwargs:
            tags.append(f"symbol:{kwargs['symbol']}")
        
        if "user_id" in kwargs:
            tags.append(f"user:{kwargs['user_id']}")
        
        return tags


class CacheOptimizationRecommendations:
    """缓存优化建议"""
    
    @staticmethod
    def analyze_hit_rate(hit_rate: float) -> List[str]:
        """分析命中率并给出建议"""
        recommendations = []
        
        if hit_rate < 0.5:
            recommendations.extend([
                "命中率过低，建议检查缓存策略",
                "考虑增加预热数据",
                "检查TTL设置是否过短",
                "优化缓存键的设计"
            ])
        elif hit_rate < 0.7:
            recommendations.extend([
                "命中率较低，建议优化缓存策略",
                "考虑增加缓存容量",
                "检查数据访问模式"
            ])
        elif hit_rate > 0.95:
            recommendations.extend([
                "命中率很高，可以考虑适当减少缓存容量",
                "检查是否有过度缓存的情况"
            ])
        
        return recommendations
    
    @staticmethod
    def analyze_memory_usage(memory_usage_ratio: float) -> List[str]:
        """分析内存使用并给出建议"""
        recommendations = []
        
        if memory_usage_ratio > 0.9:
            recommendations.extend([
                "内存使用过高，建议立即清理过期缓存",
                "考虑减少缓存容量",
                "启用数据压缩",
                "优化LRU清理策略"
            ])
        elif memory_usage_ratio > 0.8:
            recommendations.extend([
                "内存使用较高，建议监控并准备清理",
                "考虑启用数据压缩"
            ])
        elif memory_usage_ratio < 0.3:
            recommendations.extend([
                "内存使用较低，可以考虑增加缓存容量",
                "增加预热数据"
            ])
        
        return recommendations
    
    @staticmethod
    def analyze_access_pattern(hot_keys: Dict[str, int]) -> List[str]:
        """分析访问模式并给出建议"""
        recommendations = []
        
        if not hot_keys:
            recommendations.append("缺少热点数据分析，建议启用访问统计")
            return recommendations
        
        # 分析热点分布
        total_access = sum(hot_keys.values())
        top_10_access = sum(list(hot_keys.values())[:10])
        
        if top_10_access / total_access > 0.8:
            recommendations.extend([
                "访问集中度很高，建议优化热点数据的缓存策略",
                "考虑为热点数据设置更长的TTL",
                "启用数据预取功能"
            ])
        
        # 检查键模式
        key_patterns = {}
        for key in hot_keys.keys():
            pattern = ":".join(key.split(":")[:2])  # 取前两段作为模式
            key_patterns[pattern] = key_patterns.get(pattern, 0) + 1
        
        most_common_pattern = max(key_patterns.items(), key=lambda x: x[1])
        recommendations.append(
            f"最常用的键模式是 '{most_common_pattern[0]}'，"
            f"建议针对此模式优化缓存策略"
        )
        
        return recommendations
    
    @staticmethod
    def get_optimization_plan(stats: Dict[str, Any]) -> Dict[str, List[str]]:
        """生成综合优化方案"""
        plan = {
            "immediate_actions": [],
            "short_term_improvements": [],
            "long_term_optimizations": []
        }
        
        # 获取统计数据
        l1_stats = stats.get("l1_cache", {})
        l2_stats = stats.get("l2_cache", {})
        hot_keys = stats.get("hot_keys", {})
        
        # 分析各项指标
        if l1_stats.get("hit_rate", 0) < 50:
            plan["immediate_actions"].extend([
                "立即检查L1缓存配置",
                "增加预热数据",
                "优化缓存键设计"
            ])
        
        memory_ratio = l1_stats.get("memory_usage_mb", 0) / l1_stats.get("max_memory_mb", 100)
        if memory_ratio > 0.9:
            plan["immediate_actions"].extend([
                "立即清理过期缓存",
                "启用LRU清理机制",
                "考虑增加内存限制"
            ])
        
        # 短期改进
        plan["short_term_improvements"].extend([
            "实施分层缓存策略",
            "优化TTL配置",
            "启用数据压缩",
            "完善监控告警"
        ])
        
        # 长期优化
        plan["long_term_optimizations"].extend([
            "实施智能缓存预测",
            "建立缓存性能基线",
            "开发自适应缓存策略",
            "集成外部缓存服务"
        ])
        
        return plan