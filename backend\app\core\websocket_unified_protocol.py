"""
统一WebSocket协议
解决前后端WebSocket消息格式不一致的问题
提供标准化的消息结构和处理逻辑
"""

import asyncio
import json
import logging
from dataclasses import dataclass, asdict
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional, Set, Union
from fastapi import WebSocket, WebSocketDisconnect
from fastapi.websockets import WebSocketState

logger = logging.getLogger(__name__)


# 统一消息类型定义
class MessageType(str, Enum):
    """统一消息类型"""
    
    # 连接管理
    PING = "ping"
    PONG = "pong"
    CONNECT = "connect"
    DISCONNECT = "disconnect"
    
    # 订阅管理
    SUBSCRIBE = "subscribe"
    UNSUBSCRIBE = "unsubscribe"
    SUBSCRIPTION_SUCCESS = "subscription_success"
    UNSUBSCRIPTION_SUCCESS = "unsubscription_success"
    
    # 数据推送
    MARKET_DATA = "market_data"
    KLINE_DATA = "kline_data"
    TRADE_DATA = "trade_data"
    ORDER_UPDATE = "order_update"
    
    # 错误处理
    ERROR = "error"
    
    # 系统消息
    NOTIFICATION = "notification"
    STATUS = "status"


@dataclass
class WebSocketMessage:
    """统一WebSocket消息格式"""
    type: str
    data: Any = None
    id: Optional[str] = None
    timestamp: Optional[str] = None
    channel: Optional[str] = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now().isoformat()
        if self.id is None and self.type not in [MessageType.PING, MessageType.PONG]:
            import uuid
            self.id = str(uuid.uuid4())[:8]
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "type": self.type,
            "data": self.data,
            "id": self.id,
            "timestamp": self.timestamp,
            "channel": self.channel
        }
    
    def to_json(self) -> str:
        """转换为JSON字符串"""
        return json.dumps(self.to_dict(), ensure_ascii=False, default=str)


@dataclass 
class SubscriptionRequest:
    """订阅请求格式"""
    symbols: List[str]
    dataTypes: List[str]  # 保持前端命名约定
    interval: Optional[str] = None
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SubscriptionRequest':
        return cls(
            symbols=data.get('symbols', []),
            dataTypes=data.get('dataTypes', []),
            interval=data.get('interval')
        )


class UnifiedConnectionManager:
    """统一WebSocket连接管理器"""
    
    def __init__(self):
        # 活跃连接
        self.active_connections: Dict[str, WebSocket] = {}
        # 连接元数据
        self.connection_metadata: Dict[str, Dict[str, Any]] = {}
        # 订阅管理
        self.subscriptions: Dict[str, Set[str]] = {}  # topic -> client_ids
        self.client_subscriptions: Dict[str, Set[str]] = {}  # client_id -> topics
        # 心跳管理
        self.last_heartbeat: Dict[str, datetime] = {}
        
    async def connect(self, websocket: WebSocket, client_id: str) -> bool:
        """建立连接"""
        try:
            await websocket.accept()
            
            self.active_connections[client_id] = websocket
            self.connection_metadata[client_id] = {
                "connected_at": datetime.now(),
                "user_agent": websocket.headers.get("user-agent", ""),
                "client_ip": websocket.client.host if websocket.client else "unknown"
            }
            self.client_subscriptions[client_id] = set()
            self.last_heartbeat[client_id] = datetime.now()
            
            # 发送连接确认消息
            await self.send_message(client_id, WebSocketMessage(
                type=MessageType.CONNECT,
                data={
                    "status": "connected",
                    "client_id": client_id,
                    "server_time": datetime.now().isoformat()
                }
            ))
            
            logger.info(f"Client {client_id} connected")
            return True
            
        except Exception as e:
            logger.error(f"Failed to connect client {client_id}: {e}")
            return False
    
    async def disconnect(self, client_id: str):
        """断开连接"""
        if client_id not in self.active_connections:
            return
            
        # 清理订阅
        topics = self.client_subscriptions.get(client_id, set()).copy()
        for topic in topics:
            self.unsubscribe(client_id, topic)
        
        # 移除连接
        self.active_connections.pop(client_id, None)
        self.connection_metadata.pop(client_id, None)
        self.client_subscriptions.pop(client_id, None)
        self.last_heartbeat.pop(client_id, None)
        
        logger.info(f"Client {client_id} disconnected")
    
    async def send_message(self, client_id: str, message: WebSocketMessage) -> bool:
        """发送消息给指定客户端"""
        websocket = self.active_connections.get(client_id)
        if not websocket or websocket.client_state != WebSocketState.CONNECTED:
            return False
            
        try:
            await websocket.send_text(message.to_json())
            return True
        except Exception as e:
            logger.error(f"Failed to send message to {client_id}: {e}")
            await self.disconnect(client_id)
            return False
    
    async def broadcast_to_topic(self, topic: str, message: WebSocketMessage):
        """向订阅特定主题的客户端广播消息"""
        if topic not in self.subscriptions:
            return
            
        message.channel = topic
        client_ids = self.subscriptions[topic].copy()
        
        tasks = []
        for client_id in client_ids:
            if client_id in self.active_connections:
                tasks.append(self.send_message(client_id, message))
        
        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)
    
    def subscribe(self, client_id: str, topic: str) -> bool:
        """订阅主题"""
        if client_id not in self.active_connections:
            return False
            
        if topic not in self.subscriptions:
            self.subscriptions[topic] = set()
        
        self.subscriptions[topic].add(client_id)
        self.client_subscriptions[client_id].add(topic)
        
        logger.debug(f"Client {client_id} subscribed to {topic}")
        return True
    
    def unsubscribe(self, client_id: str, topic: str) -> bool:
        """取消订阅"""
        if topic in self.subscriptions:
            self.subscriptions[topic].discard(client_id)
            if not self.subscriptions[topic]:
                del self.subscriptions[topic]
        
        if client_id in self.client_subscriptions:
            self.client_subscriptions[client_id].discard(topic)
        
        logger.debug(f"Client {client_id} unsubscribed from {topic}")
        return True
    
    async def handle_heartbeat(self, client_id: str):
        """处理心跳"""
        if client_id in self.active_connections:
            self.last_heartbeat[client_id] = datetime.now()
            await self.send_message(client_id, WebSocketMessage(
                type=MessageType.PONG,
                data={"timestamp": datetime.now().isoformat()}
            ))
    
    async def handle_message(self, client_id: str, raw_message: str):
        """处理接收到的消息"""
        try:
            data = json.loads(raw_message)
            message_type = data.get("type")
            
            if message_type == MessageType.PING:
                await self.handle_heartbeat(client_id)
                
            elif message_type == MessageType.SUBSCRIBE:
                await self.handle_subscribe(client_id, data)
                
            elif message_type == MessageType.UNSUBSCRIBE:
                await self.handle_unsubscribe(client_id, data)
                
            else:
                logger.warning(f"Unknown message type from {client_id}: {message_type}")
                await self.send_message(client_id, WebSocketMessage(
                    type=MessageType.ERROR,
                    data={"message": f"Unknown message type: {message_type}"}
                ))
                
        except json.JSONDecodeError:
            await self.send_message(client_id, WebSocketMessage(
                type=MessageType.ERROR,
                data={"message": "Invalid JSON format"}
            ))
        except Exception as e:
            logger.error(f"Error handling message from {client_id}: {e}")
            await self.send_message(client_id, WebSocketMessage(
                type=MessageType.ERROR,
                data={"message": str(e)}
            ))
    
    async def handle_subscribe(self, client_id: str, data: Dict[str, Any]):
        """处理订阅请求"""
        try:
            # 支持两种订阅格式
            if "symbols" in data.get("data", {}):
                # 新格式：{"type": "subscribe", "data": {"symbols": [...], "dataTypes": [...]}}
                subscription = SubscriptionRequest.from_dict(data["data"])
                topics = [f"market.{symbol}" for symbol in subscription.symbols]
            elif "topics" in data:
                # 简单格式：{"type": "subscribe", "topics": [...]}
                topics = data["topics"]
            else:
                # 兼容旧格式
                topics = data.get("data", {}).get("topics", [])
            
            successful_topics = []
            for topic in topics:
                if self.subscribe(client_id, topic):
                    successful_topics.append(topic)
            
            await self.send_message(client_id, WebSocketMessage(
                type=MessageType.SUBSCRIPTION_SUCCESS,
                data={
                    "topics": successful_topics,
                    "message": f"Successfully subscribed to {len(successful_topics)} topics"
                }
            ))
            
        except Exception as e:
            await self.send_message(client_id, WebSocketMessage(
                type=MessageType.ERROR,
                data={"message": f"Subscription failed: {str(e)}"}
            ))
    
    async def handle_unsubscribe(self, client_id: str, data: Dict[str, Any]):
        """处理取消订阅请求"""
        try:
            topics = data.get("topics", [])
            if not topics and "data" in data:
                topics = data["data"].get("topics", [])
            
            unsubscribed_topics = []
            for topic in topics:
                if self.unsubscribe(client_id, topic):
                    unsubscribed_topics.append(topic)
            
            await self.send_message(client_id, WebSocketMessage(
                type=MessageType.UNSUBSCRIPTION_SUCCESS,
                data={
                    "topics": unsubscribed_topics,
                    "message": f"Successfully unsubscribed from {len(unsubscribed_topics)} topics"
                }
            ))
            
        except Exception as e:
            await self.send_message(client_id, WebSocketMessage(
                type=MessageType.ERROR,
                data={"message": f"Unsubscription failed: {str(e)}"}
            ))
    
    def get_stats(self) -> Dict[str, Any]:
        """获取连接统计"""
        return {
            "total_connections": len(self.active_connections),
            "total_subscriptions": sum(len(clients) for clients in self.subscriptions.values()),
            "topics": list(self.subscriptions.keys()),
            "connections": list(self.active_connections.keys())
        }


# 全局统一连接管理器
unified_connection_manager = UnifiedConnectionManager()


async def websocket_endpoint_unified(websocket: WebSocket, client_id: str = None):
    """统一WebSocket端点"""
    if not client_id:
        import uuid
        client_id = str(uuid.uuid4())[:8]
    
    if not await unified_connection_manager.connect(websocket, client_id):
        return
    
    try:
        while True:
            data = await websocket.receive_text()
            await unified_connection_manager.handle_message(client_id, data)
            
    except WebSocketDisconnect:
        logger.info(f"Client {client_id} disconnected normally")
    except Exception as e:
        logger.error(f"WebSocket error for client {client_id}: {e}")
    finally:
        await unified_connection_manager.disconnect(client_id)


# 业务功能函数
async def push_market_data(symbol: str, data: Dict[str, Any]):
    """推送市场数据"""
    message = WebSocketMessage(
        type=MessageType.MARKET_DATA,
        data={
            "symbol": symbol,
            **data
        }
    )
    await unified_connection_manager.broadcast_to_topic(f"market.{symbol}", message)


async def push_kline_data(symbol: str, interval: str, data: List[Dict[str, Any]]):
    """推送K线数据"""
    message = WebSocketMessage(
        type=MessageType.KLINE_DATA,
        data={
            "symbol": symbol,
            "interval": interval,
            "klines": data
        }
    )
    await unified_connection_manager.broadcast_to_topic(f"kline.{symbol}.{interval}", message)


async def push_notification(message: str, level: str = "info"):
    """推送系统通知"""
    notification = WebSocketMessage(
        type=MessageType.NOTIFICATION,
        data={
            "message": message,
            "level": level,
            "timestamp": datetime.now().isoformat()
        }
    )
    # 广播给所有连接
    for client_id in unified_connection_manager.active_connections:
        await unified_connection_manager.send_message(client_id, notification)