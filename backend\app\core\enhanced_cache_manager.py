"""
增强的缓存管理系统
提供智能缓存策略，确保数据一致性和性能优化
"""

import asyncio
import json
import logging
import time
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Set, Tuple, Union
from collections import defaultdict
from dataclasses import dataclass
from enum import Enum

from app.core.unified_cache import unified_cache
from app.core.config import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()


class CacheLevel(str, Enum):
    """缓存级别"""
    L1_MEMORY = "l1_memory"      # 内存缓存 - 最快
    L2_REDIS = "l2_redis"        # Redis缓存 - 中等
    L3_DATABASE = "l3_database"  # 数据库缓存 - 最慢


class CacheStrategy(str, Enum):
    """缓存策略"""
    WRITE_THROUGH = "write_through"    # 写穿透
    WRITE_BACK = "write_back"          # 写回
    WRITE_AROUND = "write_around"      # 写绕过
    READ_THROUGH = "read_through"      # 读穿透


@dataclass
class CacheEntry:
    """缓存条目"""
    key: str
    value: Any
    created_at: datetime
    expires_at: datetime
    access_count: int = 0
    last_accessed: datetime = None
    size_bytes: int = 0
    tags: Set[str] = None
    
    def __post_init__(self):
        if self.last_accessed is None:
            self.last_accessed = self.created_at
        if self.tags is None:
            self.tags = set()
        if self.size_bytes == 0:
            # 估算缓存项大小
            self.size_bytes = self._estimate_size()
    
    def _estimate_size(self) -> int:
        """估算缓存项大小（字节）"""
        try:
            if isinstance(self.value, (str, int, float, bool)):
                return len(str(self.value))
            elif isinstance(self.value, (list, dict)):
                return len(json.dumps(self.value, default=str))
            else:
                return len(str(self.value))
        except Exception:
            return 1024  # 默认1KB
    
    def is_expired(self) -> bool:
        """检查是否过期"""
        return datetime.now() >= self.expires_at
    
    def refresh_access(self):
        """刷新访问时间"""
        self.access_count += 1
        self.last_accessed = datetime.now()


class IntelligentCacheManager:
    """智能缓存管理器"""
    
    def __init__(self):
        # L1内存缓存
        self.l1_cache: Dict[str, CacheEntry] = {}
        self.l1_max_size = 10000  # 最大条目数
        self.l1_max_memory_mb = 100  # 最大内存使用（MB）
        
        # 缓存统计
        self.stats = {
            "l1_hits": 0,
            "l1_misses": 0,
            "l2_hits": 0,
            "l2_misses": 0,
            "writes": 0,
            "evictions": 0,
            "errors": 0
        }
        
        # 热点数据跟踪
        self.hot_keys: Dict[str, int] = defaultdict(int)  # key -> access_count
        self.key_patterns: Dict[str, List[str]] = defaultdict(list)  # pattern -> keys
        
        # 缓存预热配置
        self.warmup_patterns = [
            "market:basic:*",     # 基础市场数据
            "stock:info:*",       # 股票信息
            "quote:realtime:*",   # 实时行情
        ]
        
        # 异步任务
        self.background_tasks: Set[asyncio.Task] = set()
        self.is_running = False
    
    async def get(self, key: str, cache_level: CacheLevel = CacheLevel.L1_MEMORY) -> Optional[Any]:
        """获取缓存数据"""
        try:
            # L1内存缓存查找
            if key in self.l1_cache:
                entry = self.l1_cache[key]
                if not entry.is_expired():
                    entry.refresh_access()
                    self.stats["l1_hits"] += 1
                    self._track_hot_key(key)
                    return entry.value
                else:
                    # 过期删除
                    del self.l1_cache[key]
            
            self.stats["l1_misses"] += 1
            
            # L2 Redis缓存查找
            if cache_level in [CacheLevel.L2_REDIS, CacheLevel.L1_MEMORY]:
                redis_value = await unified_cache.get(key)
                if redis_value is not None:
                    self.stats["l2_hits"] += 1
                    
                    # 如果是热点数据，存入L1缓存
                    if self._is_hot_key(key):
                        await self._promote_to_l1(key, redis_value)
                    
                    return redis_value
            
            self.stats["l2_misses"] += 1
            return None
            
        except Exception as e:
            logger.error(f"缓存获取失败 {key}: {e}")
            self.stats["errors"] += 1
            return None
    
    async def set(self, key: str, value: Any, ttl: Optional[int] = None,
                 cache_level: CacheLevel = CacheLevel.L1_MEMORY,
                 tags: Optional[Set[str]] = None) -> bool:
        """设置缓存数据"""
        try:
            self.stats["writes"] += 1
            
            # 默认TTL
            if ttl is None:
                ttl = self._get_default_ttl(key)
            
            expires_at = datetime.now() + timedelta(seconds=ttl)
            
            # 根据缓存级别存储
            success = True
            
            # L1内存缓存
            if cache_level == CacheLevel.L1_MEMORY:
                # 检查内存限制
                if await self._should_store_in_l1(key, value):
                    entry = CacheEntry(
                        key=key,
                        value=value,
                        created_at=datetime.now(),
                        expires_at=expires_at,
                        tags=tags or set()
                    )
                    self.l1_cache[key] = entry
                    
                    # 检查是否需要清理
                    await self._cleanup_l1_if_needed()
            
            # L2 Redis缓存
            if cache_level in [CacheLevel.L2_REDIS, CacheLevel.L1_MEMORY]:
                success &= await unified_cache.set(key, value, ttl)
            
            return success
            
        except Exception as e:
            logger.error(f"缓存设置失败 {key}: {e}")
            self.stats["errors"] += 1
            return False
    
    async def delete(self, key: str) -> bool:
        """删除缓存数据"""
        try:
            # L1缓存删除
            if key in self.l1_cache:
                del self.l1_cache[key]
            
            # L2缓存删除
            await unified_cache.delete(key)
            
            return True
            
        except Exception as e:
            logger.error(f"缓存删除失败 {key}: {e}")
            return False
    
    async def delete_by_pattern(self, pattern: str) -> int:
        """按模式删除缓存"""
        try:
            deleted_count = 0
            
            # L1缓存模式删除
            import fnmatch
            keys_to_delete = [
                key for key in self.l1_cache.keys()
                if fnmatch.fnmatch(key, pattern)
            ]
            
            for key in keys_to_delete:
                del self.l1_cache[key]
                deleted_count += 1
            
            # L2缓存模式删除
            redis_deleted = await unified_cache.delete_pattern(pattern)
            deleted_count += redis_deleted
            
            return deleted_count
            
        except Exception as e:
            logger.error(f"模式删除失败 {pattern}: {e}")
            return 0
    
    async def delete_by_tags(self, tags: Set[str]) -> int:
        """按标签删除缓存"""
        try:
            deleted_count = 0
            
            # L1缓存按标签删除
            keys_to_delete = []
            for key, entry in self.l1_cache.items():
                if entry.tags & tags:  # 有交集
                    keys_to_delete.append(key)
            
            for key in keys_to_delete:
                del self.l1_cache[key]
                deleted_count += 1
            
            # TODO: L2缓存按标签删除（需要Redis支持）
            
            return deleted_count
            
        except Exception as e:
            logger.error(f"标签删除失败 {tags}: {e}")
            return 0
    
    async def refresh_cache(self, key: str, loader_func) -> Optional[Any]:
        """刷新缓存数据"""
        try:
            # 调用加载函数获取新数据
            new_value = await loader_func() if asyncio.iscoroutinefunction(loader_func) else loader_func()
            
            if new_value is not None:
                await self.set(key, new_value)
                return new_value
            
            return None
            
        except Exception as e:
            logger.error(f"缓存刷新失败 {key}: {e}")
            return None
    
    async def warmup(self, patterns: Optional[List[str]] = None) -> Dict[str, int]:
        """缓存预热"""
        try:
            patterns = patterns or self.warmup_patterns
            warmup_stats = {}
            
            for pattern in patterns:
                try:
                    # 根据模式预热数据
                    count = await self._warmup_pattern(pattern)
                    warmup_stats[pattern] = count
                    
                except Exception as e:
                    logger.error(f"预热模式失败 {pattern}: {e}")
                    warmup_stats[pattern] = 0
            
            total_warmed = sum(warmup_stats.values())
            logger.info(f"缓存预热完成: {total_warmed} 个条目")
            
            return warmup_stats
            
        except Exception as e:
            logger.error(f"缓存预热失败: {e}")
            return {}
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        l1_memory_usage = sum(
            entry.size_bytes for entry in self.l1_cache.values()
        )
        
        # 计算命中率
        total_l1 = self.stats["l1_hits"] + self.stats["l1_misses"]
        total_l2 = self.stats["l2_hits"] + self.stats["l2_misses"]
        
        l1_hit_rate = self.stats["l1_hits"] / total_l1 if total_l1 > 0 else 0
        l2_hit_rate = self.stats["l2_hits"] / total_l2 if total_l2 > 0 else 0
        
        return {
            "l1_cache": {
                "entries": len(self.l1_cache),
                "max_entries": self.l1_max_size,
                "memory_usage_bytes": l1_memory_usage,
                "memory_usage_mb": l1_memory_usage / 1024 / 1024,
                "max_memory_mb": self.l1_max_memory_mb,
                "hit_rate": round(l1_hit_rate * 100, 2),
                "hits": self.stats["l1_hits"],
                "misses": self.stats["l1_misses"]
            },
            "l2_cache": {
                "hit_rate": round(l2_hit_rate * 100, 2),
                "hits": self.stats["l2_hits"],
                "misses": self.stats["l2_misses"]
            },
            "operations": {
                "writes": self.stats["writes"],
                "evictions": self.stats["evictions"],
                "errors": self.stats["errors"]
            },
            "hot_keys": dict(list(self.hot_keys.items())[:10])  # 前10个热点key
        }
    
    def _track_hot_key(self, key: str):
        """跟踪热点key"""
        self.hot_keys[key] += 1
    
    def _is_hot_key(self, key: str) -> bool:
        """判断是否为热点key"""
        return self.hot_keys.get(key, 0) > 5  # 访问次数超过5次
    
    def _get_default_ttl(self, key: str) -> int:
        """根据key模式获取默认TTL"""
        if key.startswith("rt:") or key.startswith("realtime:"):
            return 30  # 实时数据30秒
        elif key.startswith("quote:"):
            return 300  # 行情数据5分钟
        elif key.startswith("kline:"):
            return 900  # K线数据15分钟
        elif key.startswith("stock:"):
            return 3600  # 股票信息1小时
        elif key.startswith("historical:"):
            return 86400  # 历史数据1天
        else:
            return 1800  # 默认30分钟
    
    async def _should_store_in_l1(self, key: str, value: Any) -> bool:
        """判断是否应该存储在L1缓存"""
        # 检查条目数限制
        if len(self.l1_cache) >= self.l1_max_size:
            return False
        
        # 检查内存使用限制
        current_memory = sum(entry.size_bytes for entry in self.l1_cache.values())
        if current_memory >= self.l1_max_memory_mb * 1024 * 1024:
            return False
        
        # 热点数据优先存储
        if self._is_hot_key(key):
            return True
        
        # 小数据优先存储
        estimated_size = len(str(value))
        if estimated_size < 10240:  # 小于10KB
            return True
        
        return False
    
    async def _cleanup_l1_if_needed(self):
        """L1缓存清理"""
        if len(self.l1_cache) <= self.l1_max_size:
            return
        
        # LFU策略：删除访问次数最少的条目
        sorted_entries = sorted(
            self.l1_cache.items(),
            key=lambda x: (x[1].access_count, x[1].last_accessed)
        )
        
        # 删除25%的最少使用条目
        cleanup_count = max(1, len(sorted_entries) // 4)
        
        for i in range(cleanup_count):
            key, entry = sorted_entries[i]
            del self.l1_cache[key]
            self.stats["evictions"] += 1
    
    async def _promote_to_l1(self, key: str, value: Any):
        """将热点数据提升到L1缓存"""
        if await self._should_store_in_l1(key, value):
            ttl = self._get_default_ttl(key)
            entry = CacheEntry(
                key=key,
                value=value,
                created_at=datetime.now(),
                expires_at=datetime.now() + timedelta(seconds=ttl)
            )
            self.l1_cache[key] = entry
    
    async def _warmup_pattern(self, pattern: str) -> int:
        """预热指定模式的数据"""
        # 这里应该根据具体的数据源实现预热逻辑
        # 例如：从数据库加载常用数据到缓存
        
        if pattern == "market:basic:*":
            # 预热基础市场数据
            return await self._warmup_market_basics()
        elif pattern == "stock:info:*":
            # 预热股票信息
            return await self._warmup_stock_info()
        elif pattern == "quote:realtime:*":
            # 预热实时行情
            return await self._warmup_realtime_quotes()
        
        return 0
    
    async def _warmup_market_basics(self) -> int:
        """预热基础市场数据"""
        try:
            # 模拟数据预热
            basic_data = {
                "market_status": "open",
                "trading_day": datetime.now().strftime("%Y-%m-%d"),
                "indices": ["000001", "399001", "000300"]
            }
            
            await self.set("market:basic:status", basic_data)
            return 1
            
        except Exception as e:
            logger.error(f"市场基础数据预热失败: {e}")
            return 0
    
    async def _warmup_stock_info(self) -> int:
        """预热股票信息"""
        try:
            # 预热热门股票信息
            hot_stocks = ["000001", "000002", "600000", "600036", "600519"]
            count = 0
            
            for symbol in hot_stocks:
                stock_info = {
                    "symbol": symbol,
                    "name": f"股票{symbol}",
                    "market": "SZ" if symbol.startswith("00") else "SH"
                }
                await self.set(f"stock:info:{symbol}", stock_info)
                count += 1
            
            return count
            
        except Exception as e:
            logger.error(f"股票信息预热失败: {e}")
            return 0
    
    async def _warmup_realtime_quotes(self) -> int:
        """预热实时行情"""
        # 实时行情数据通常不需要预热，因为它们变化很快
        return 0
    
    async def start_background_tasks(self):
        """启动后台任务"""
        if self.is_running:
            return
        
        self.is_running = True
        
        # 启动清理任务
        cleanup_task = asyncio.create_task(self._background_cleanup())
        self.background_tasks.add(cleanup_task)
        cleanup_task.add_done_callback(self.background_tasks.discard)
        
        # 启动统计任务
        stats_task = asyncio.create_task(self._background_stats())
        self.background_tasks.add(stats_task)
        stats_task.add_done_callback(self.background_tasks.discard)
        
        logger.info("缓存管理器后台任务已启动")
    
    async def stop_background_tasks(self):
        """停止后台任务"""
        self.is_running = False
        
        for task in self.background_tasks:
            task.cancel()
        
        await asyncio.gather(*self.background_tasks, return_exceptions=True)
        self.background_tasks.clear()
        
        logger.info("缓存管理器后台任务已停止")
    
    async def _background_cleanup(self):
        """后台清理任务"""
        while self.is_running:
            try:
                # 清理过期条目
                expired_keys = [
                    key for key, entry in self.l1_cache.items()
                    if entry.is_expired()
                ]
                
                for key in expired_keys:
                    del self.l1_cache[key]
                
                if expired_keys:
                    logger.debug(f"清理了 {len(expired_keys)} 个过期缓存条目")
                
                # 每分钟执行一次
                await asyncio.sleep(60)
                
            except Exception as e:
                logger.error(f"后台清理任务错误: {e}")
                await asyncio.sleep(60)
    
    async def _background_stats(self):
        """后台统计任务"""
        while self.is_running:
            try:
                # 每10分钟记录一次统计信息
                stats = self.get_stats()
                logger.info(f"缓存统计: L1命中率={stats['l1_cache']['hit_rate']}%, "
                           f"L2命中率={stats['l2_cache']['hit_rate']}%, "
                           f"内存使用={stats['l1_cache']['memory_usage_mb']:.1f}MB")
                
                await asyncio.sleep(600)  # 10分钟
                
            except Exception as e:
                logger.error(f"后台统计任务错误: {e}")
                await asyncio.sleep(600)


# 全局增强缓存管理器实例
enhanced_cache_manager = IntelligentCacheManager()