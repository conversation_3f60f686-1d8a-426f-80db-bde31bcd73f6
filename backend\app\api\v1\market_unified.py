"""
统一市场数据API
整合所有重复的市场API定义，提供标准化接口
解决API路由冲突和重复定义问题
"""

import logging
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, Request
from fastapi.responses import JSONResponse

from app.core.cache_unified import (
    DataType, 
    get_cached_market_data, 
    cache_market_data,
    get_cached_kline_data,
    cache_kline_data,
    unified_cache_manager
)
from app.core.config import settings
from app.core.optional_auth import OptionalAuth
from app.core.websocket_unified_protocol import push_market_data, push_kline_data
from app.middleware.rate_limit_middleware import market_rate_limiter

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(tags=["市场数据统一API"])

# 初始化可选认证
optional_auth = OptionalAuth()


# 数据源管理
class DataSourceManager:
    """数据源管理器"""
    
    def __init__(self):
        self.available_sources = []
        self.current_source = "mock"
        self._initialize_sources()
    
    def _initialize_sources(self):
        """初始化可用数据源"""
        self.available_sources = settings.data_source_priority
        if self.available_sources:
            self.current_source = self.available_sources[0]
        logger.info(f"Data sources initialized: {self.available_sources}, current: {self.current_source}")
    
    async def get_market_data(self, symbol: str) -> Optional[Dict[str, Any]]:
        """获取市场数据 - 按优先级尝试数据源"""
        
        # 1. 先尝试缓存
        cached_data = await get_cached_market_data(symbol)
        if cached_data:
            logger.debug(f"Market data for {symbol} retrieved from cache")
            return cached_data
        
        # 2. 按优先级尝试数据源
        for source in self.available_sources:
            try:
                data = await self._fetch_from_source(source, symbol)
                if data:
                    # 缓存数据
                    await cache_market_data(symbol, data)
                    # 推送WebSocket
                    await push_market_data(symbol, data)
                    logger.info(f"Market data for {symbol} fetched from {source}")
                    return data
            except Exception as e:
                logger.warning(f"Failed to fetch {symbol} from {source}: {e}")
                continue
        
        logger.error(f"All data sources failed for symbol {symbol}")
        return None
    
    async def _fetch_from_source(self, source: str, symbol: str) -> Optional[Dict[str, Any]]:
        """从指定数据源获取数据"""
        
        if source == "tushare":
            return await self._fetch_from_tushare(symbol)
        elif source == "akshare":
            return await self._fetch_from_akshare(symbol)
        elif source == "mock":
            return await self._fetch_from_mock(symbol)
        else:
            logger.warning(f"Unknown data source: {source}")
            return None
    
    async def _fetch_from_tushare(self, symbol: str) -> Optional[Dict[str, Any]]:
        """从Tushare获取数据"""
        try:
            # 这里应该导入并使用Tushare适配器
            from app.services.adapters.tushare_adapter import tushare_adapter
            return await tushare_adapter.get_quote(symbol)
        except Exception as e:
            logger.error(f"Tushare fetch error for {symbol}: {e}")
            return None
    
    async def _fetch_from_akshare(self, symbol: str) -> Optional[Dict[str, Any]]:
        """从AkShare获取数据"""
        try:
            from app.services.akshare_data_service import akshare_data_service
            return await akshare_data_service.get_realtime_quote(symbol)
        except Exception as e:
            logger.error(f"AkShare fetch error for {symbol}: {e}")
            return None
    
    async def _fetch_from_mock(self, symbol: str) -> Dict[str, Any]:
        """从Mock数据源获取数据"""
        import random
        
        # 生成Mock数据
        base_price = 10.0 + random.uniform(0, 100)
        change = random.uniform(-5, 5)
        
        return {
            "symbol": symbol,
            "name": f"测试股票{symbol}",
            "current_price": round(base_price, 2),
            "change": round(change, 2),
            "change_percent": round((change / base_price) * 100, 2),
            "volume": random.randint(1000000, 50000000),
            "timestamp": datetime.now().isoformat(),
            "source": "mock"
        }

    async def get_kline_data(self, symbol: str, interval: str = "1d", limit: int = 100) -> List[Dict[str, Any]]:
        """获取K线数据"""
        
        # 1. 尝试缓存
        cached_data = await get_cached_kline_data(symbol, interval)
        if cached_data:
            return cached_data[:limit]
        
        # 2. 按优先级获取数据
        for source in self.available_sources:
            try:
                data = await self._fetch_kline_from_source(source, symbol, interval, limit)
                if data:
                    # 缓存数据
                    await cache_kline_data(symbol, interval, data)
                    # 推送WebSocket
                    await push_kline_data(symbol, interval, data)
                    return data
            except Exception as e:
                logger.warning(f"Failed to fetch kline {symbol} from {source}: {e}")
                continue
        
        # 3. 返回Mock数据作为兜底
        return await self._generate_mock_kline(symbol, interval, limit)
    
    async def _fetch_kline_from_source(self, source: str, symbol: str, interval: str, limit: int) -> Optional[List[Dict[str, Any]]]:
        """从指定源获取K线数据"""
        
        if source == "tushare":
            try:
                from app.services.adapters.tushare_adapter import tushare_adapter
                return await tushare_adapter.get_kline(symbol, interval, limit)
            except:
                return None
                
        elif source == "akshare":
            try:
                from app.services.akshare_data_service import akshare_data_service
                return await akshare_data_service.get_kline_data(symbol, interval, limit)
            except:
                return None
                
        elif source == "mock":
            return await self._generate_mock_kline(symbol, interval, limit)
        
        return None
    
    async def _generate_mock_kline(self, symbol: str, interval: str, limit: int) -> List[Dict[str, Any]]:
        """生成Mock K线数据"""
        import random
        from datetime import timedelta
        
        data = []
        base_price = 10.0 + random.uniform(0, 100)
        current_time = datetime.now()
        
        # 根据interval计算时间间隔
        interval_minutes = {
            "1m": 1, "5m": 5, "15m": 15, "30m": 30,
            "1h": 60, "1d": 1440, "1w": 10080
        }.get(interval, 1440)
        
        for i in range(limit):
            timestamp = current_time - timedelta(minutes=interval_minutes * (limit - i))
            
            # 生成OHLCV数据
            open_price = base_price + random.uniform(-2, 2)
            high_price = open_price + random.uniform(0, 3)
            low_price = open_price - random.uniform(0, 2)
            close_price = open_price + random.uniform(-1.5, 1.5)
            volume = random.randint(100000, 1000000)
            
            data.append({
                "timestamp": timestamp.isoformat(),
                "open": round(open_price, 2),
                "high": round(high_price, 2),
                "low": round(low_price, 2),
                "close": round(close_price, 2),
                "volume": volume,
                "symbol": symbol,
                "interval": interval
            })
            
            base_price = close_price  # 下一个K线的基础价格
        
        return data


# 全局数据源管理器
data_source_manager = DataSourceManager()


# API端点定义
@router.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "data_sources": data_source_manager.available_sources,
        "current_source": data_source_manager.current_source
    }


@router.get("/overview")
async def get_market_overview(
    request: Request,
    user = Depends(optional_auth.get_current_user)
):
    """获取市场概览"""
    
    # 检查限流
    rate_limit_response = await market_rate_limiter.check_rate_limit(
        request, str(user.id) if user else None
    )
    if rate_limit_response:
        return rate_limit_response
    
    try:
        # 初始化缓存
        await unified_cache_manager.initialize()
        
        # 构建市场概览数据
        overview = {
            "timestamp": datetime.now().isoformat(),
            "indices": {
                "000001": {
                    "name": "上证指数",
                    "current_price": 3245.68,
                    "change": 12.45,
                    "change_percent": 0.38
                },
                "399001": {
                    "name": "深证成指", 
                    "current_price": 10856.34,
                    "change": -23.67,
                    "change_percent": -0.22
                }
            },
            "market_stats": {
                "total_stocks": 4500,
                "advancing": 1250,
                "declining": 987,
                "unchanged": 263
            },
            "data_source": data_source_manager.current_source
        }
        
        return {
            "success": True,
            "data": overview,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Market overview error: {e}")
        raise HTTPException(status_code=500, detail="获取市场概览失败")


@router.get("/quote/{symbol}")
async def get_quote(
    symbol: str,
    request: Request,
    user = Depends(optional_auth.get_current_user)
):
    """获取单个股票行情"""
    
    # 检查限流
    rate_limit_response = await market_rate_limiter.check_rate_limit(
        request, str(user.id) if user else None
    )
    if rate_limit_response:
        return rate_limit_response
    
    try:
        # 确保缓存初始化
        await unified_cache_manager.initialize()
        
        # 获取行情数据
        quote_data = await data_source_manager.get_market_data(symbol)
        if not quote_data:
            raise HTTPException(status_code=404, detail=f"股票 {symbol} 不存在或数据获取失败")
        
        return {
            "success": True,
            "data": quote_data,
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Get quote error for {symbol}: {e}")
        raise HTTPException(status_code=500, detail="获取行情数据失败")


@router.post("/quotes")
async def get_quotes(
    symbols: List[str],
    request: Request,
    user = Depends(optional_auth.get_current_user)
):
    """批量获取股票行情"""
    
    # 检查限流
    rate_limit_response = await market_rate_limiter.check_rate_limit(
        request, str(user.id) if user else None
    )
    if rate_limit_response:
        return rate_limit_response
    
    # 限制批量查询数量
    if len(symbols) > 50:
        raise HTTPException(status_code=400, detail="单次查询股票数量不能超过50只")
    
    try:
        await unified_cache_manager.initialize()
        
        results = []
        for symbol in symbols:
            quote_data = await data_source_manager.get_market_data(symbol)
            if quote_data:
                results.append(quote_data)
        
        return {
            "success": True,
            "data": results,
            "total": len(results),
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Batch quotes error: {e}")
        raise HTTPException(status_code=500, detail="批量获取行情失败")


@router.get("/kline/{symbol}")
async def get_kline(
    symbol: str,
    request: Request,
    interval: str = Query("1d", regex="^(1m|5m|15m|30m|1h|1d|1w)$"),
    limit: int = Query(100, ge=1, le=1000),
    user = Depends(optional_auth.get_current_user)
):
    """获取K线数据"""
    
    # 检查限流
    rate_limit_response = await market_rate_limiter.check_rate_limit(
        request, str(user.id) if user else None
    )
    if rate_limit_response:
        return rate_limit_response
    
    try:
        await unified_cache_manager.initialize()
        
        kline_data = await data_source_manager.get_kline_data(symbol, interval, limit)
        
        return {
            "success": True,
            "data": {
                "symbol": symbol,
                "interval": interval,
                "klines": kline_data
            },
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Get kline error for {symbol}: {e}")
        raise HTTPException(status_code=500, detail="获取K线数据失败")


@router.get("/search")
async def search_stocks(
    query: str = Query(..., min_length=1, max_length=20),
    request: Request,
    limit: int = Query(20, ge=1, le=50),
    user = Depends(optional_auth.get_current_user)
):
    """搜索股票"""
    
    # 检查限流
    rate_limit_response = await market_rate_limiter.check_rate_limit(
        request, str(user.id) if user else None
    )
    if rate_limit_response:
        return rate_limit_response
    
    try:
        # Mock搜索结果
        results = []
        if query.isdigit():
            # 如果是数字，假设搜索股票代码
            for i in range(min(limit, 5)):
                symbol = f"{query}{i:02d}" if len(query) <= 4 else query
                results.append({
                    "symbol": symbol,
                    "name": f"股票{symbol}",
                    "market": "SH" if symbol.startswith("6") else "SZ",
                    "type": "stock"
                })
        else:
            # 文字搜索
            for i in range(min(limit, 10)):
                results.append({
                    "symbol": f"00000{i}",
                    "name": f"{query}概念{i}",
                    "market": "SZ",
                    "type": "stock"
                })
        
        return {
            "success": True,
            "data": results,
            "query": query,
            "total": len(results),
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Search error for '{query}': {e}")
        raise HTTPException(status_code=500, detail="搜索失败")


@router.get("/cache/stats")
async def get_cache_stats():
    """获取缓存统计信息"""
    try:
        await unified_cache_manager.initialize()
        stats = unified_cache_manager.get_stats()
        return {
            "success": True,
            "data": stats,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"Get cache stats error: {e}")
        raise HTTPException(status_code=500, detail="获取缓存统计失败")


@router.delete("/cache/clear")
async def clear_cache(
    cache_type: Optional[str] = Query(None, regex="^(realtime|market_quote|kline_data|all)$")
):
    """清理缓存"""
    try:
        await unified_cache_manager.initialize()
        
        if cache_type == "all":
            count = await unified_cache_manager.clear_all()
        elif cache_type == "realtime":
            count = await unified_cache_manager.clear_type(DataType.REALTIME)
        elif cache_type == "market_quote":
            count = await unified_cache_manager.clear_type(DataType.MARKET_QUOTE)
        elif cache_type == "kline_data":
            count = await unified_cache_manager.clear_type(DataType.KLINE_DATA)
        else:
            # 默认清理实时数据缓存
            count = await unified_cache_manager.clear_type(DataType.REALTIME)
        
        return {
            "success": True,
            "data": {
                "cleared_count": count,
                "cache_type": cache_type or "realtime"
            },
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Clear cache error: {e}")
        raise HTTPException(status_code=500, detail="清理缓存失败")


# 启动时初始化
@router.on_event("startup")
async def startup_event():
    """启动时初始化"""
    try:
        await unified_cache_manager.initialize()
        logger.info("Market unified API initialized")
    except Exception as e:
        logger.error(f"Market unified API startup error: {e}")


@router.on_event("shutdown") 
async def shutdown_event():
    """关闭时清理"""
    try:
        await unified_cache_manager.close()
        logger.info("Market unified API shutdown")
    except Exception as e:
        logger.error(f"Market unified API shutdown error: {e}")