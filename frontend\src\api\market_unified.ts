/**
 * 统一市场数据API - 修复版本
 * 统一前后端接口路径，解决路径不匹配问题
 */
import { httpClient } from './http'
import type {
  QuoteData,
  KLineData,
  KLineParams,
  QuoteParams,
  SearchParams,
  SearchResult,
  MarketOverview,
  SectorData,
  NewsData,
  RankingData,
  RankingType,
  MarketStatus,
  OrderBookData,
  StockSearchResult,
  WatchlistItem,
  DepthData,
  MarketSector,
  NewsItem,
  IndexData
} from '@/types/market'
import { mockService } from '@/services/mock.service'
import type {
  ApiResponse,
  ListResponse,
  TickData,
  BarData,
  QueryParams
} from '@/types/api'

// 配置
const USE_MOCK = import.meta.env.VITE_USE_MOCK === 'true' || import.meta.env.DEV

// 统一的API路径前缀
const API_BASE = '/api/v1/market'

export interface MarketDataRequest {
  symbol: string
  startDate?: string
  endDate?: string
  interval?: string
  limit?: number
}

export interface SymbolInfo {
  symbol: string
  name: string
  exchange: string
  type: string
  minTick: number
  multiplier: number
  marginRate: number
  feeRate: number
  status: string
}

/**
 * 统一市场数据API类
 */
export class UnifiedMarketAPI {
  /**
   * 获取实时行情
   */
  async getQuote(symbols: string | string[]): Promise<QuoteData[]> {
    const symbolList = Array.isArray(symbols) ? symbols : [symbols]

    if (USE_MOCK) {
      return mockService.getQuoteData(symbolList)
    }

    try {
      const response = await httpClient.get<any>(`${API_BASE}/quotes/realtime`, {
        params: {
          symbols: symbolList.join(',')
        }
      })

      // 处理统一响应格式
      if (response.data?.success && response.data?.data?.quotes) {
        return response.data.data.quotes
      }

      return response.data || []
    } catch (error) {
      console.warn('实时行情API调用失败，使用模拟数据:', error)
      return mockService.getQuoteData(symbolList)
    }
  }

  /**
   * 获取单个股票行情
   */
  async getQuoteDetail(symbol: string): Promise<QuoteData | null> {
    if (USE_MOCK) {
      const quotes = await mockService.getQuoteData([symbol])
      return quotes[0] || null
    }

    try {
      const response = await httpClient.get<any>(`${API_BASE}/quotes/${symbol}`)

      if (response.data?.success && response.data?.data) {
        return response.data.data
      }

      return response.data || null
    } catch (error) {
      console.warn(`获取股票${symbol}行情失败，使用模拟数据:`, error)
      const quotes = await mockService.getQuoteData([symbol])
      return quotes[0] || null
    }
  }

  /**
   * 批量获取行情数据
   */
  async getQuotes(params: QuoteParams): Promise<QuoteData[]> {
    if (USE_MOCK) {
      return mockService.getQuoteData(params.symbols)
    }

    try {
      const response = await httpClient.get<any>(`${API_BASE}/quotes/realtime`, {
        params: {
          symbols: params.symbols.join(','),
          fields: params.fields?.join(',')
        }
      })

      if (response.data?.success && response.data?.data?.quotes) {
        return response.data.data.quotes
      }

      return response.data || []
    } catch (error) {
      console.warn('批量获取行情失败，使用模拟数据:', error)
      return mockService.getQuoteData(params.symbols)
    }
  }

  /**
   * 获取K线数据
   */
  async getKLineData(params: KLineParams): Promise<KLineData[]> {
    if (USE_MOCK) {
      return mockService.getKLineData(params)
    }

    try {
      const response = await httpClient.get<any>(`${API_BASE}/kline/${params.symbol}`, {
        params: {
          period: params.period,
          limit: params.limit || 1000,
          start_date: params.startTime,
          end_date: params.endTime
        }
      })

      if (response.data?.success && response.data?.data?.klines) {
        return response.data.data.klines
      }

      return response.data || []
    } catch (error) {
      console.warn('获取K线数据失败，使用模拟数据:', error)
      return mockService.getKLineData(params)
    }
  }

  /**
   * 获取历史K线数据
   */
  async getHistoryKLineData(params: KLineParams): Promise<KLineData[]> {
    return this.getKLineData(params) // 统一使用同一个接口
  }

  /**
   * 获取五档盘口数据
   */
  async getOrderBook(symbol: string): Promise<any> {
    if (USE_MOCK) {
      // 生成模拟五档盘口数据
      const basePrice = 10.0 + Math.random() * 2
      const bid = []
      const ask = []

      for (let i = 0; i < 5; i++) {
        bid.push({
          price: Number((basePrice - (i + 1) * 0.01).toFixed(2)),
          volume: Math.floor(Math.random() * 5000) * 100,
          level: i + 1
        })
        ask.push({
          price: Number((basePrice + (i + 1) * 0.01).toFixed(2)),
          volume: Math.floor(Math.random() * 5000) * 100,
          level: i + 1
        })
      }

      return {
        symbol,
        bid,
        ask,
        timestamp: new Date().toISOString()
      }
    }

    try {
      const response = await httpClient.get<any>(`${API_BASE}/quotes/${symbol}/orderbook`)

      if (response.data?.success && response.data?.data) {
        return response.data.data
      }

      return response.data || null
    } catch (error) {
      console.warn(`获取股票${symbol}五档盘口失败，使用模拟数据:`, error)
      // 返回模拟数据
      const basePrice = 10.0 + Math.random() * 2
      const bid = []
      const ask = []

      for (let i = 0; i < 5; i++) {
        bid.push({
          price: Number((basePrice - (i + 1) * 0.01).toFixed(2)),
          volume: Math.floor(Math.random() * 5000) * 100,
          level: i + 1
        })
        ask.push({
          price: Number((basePrice + (i + 1) * 0.01).toFixed(2)),
          volume: Math.floor(Math.random() * 5000) * 100,
          level: i + 1
        })
      }

      return {
        symbol,
        bid,
        ask,
        timestamp: new Date().toISOString()
      }
    }
  }

  /**
   * 获取逐笔成交数据
   */
  async getRecentTrades(symbol: string, limit: number = 50): Promise<any> {
    if (USE_MOCK) {
      // 生成模拟逐笔成交数据
      const trades = []
      const basePrice = 10.0 + Math.random() * 2
      const now = new Date()

      for (let i = 0; i < limit; i++) {
        const tradeTime = new Date(now.getTime() - i * Math.random() * 10000)
        const price = Number((basePrice + (Math.random() - 0.5) * 0.1).toFixed(2))
        const volume = [100, 200, 300, 500, 1000][Math.floor(Math.random() * 5)]
        const direction = ['buy', 'sell', 'neutral'][Math.floor(Math.random() * 3)]

        trades.push({
          time: tradeTime.toTimeString().slice(0, 8),
          timestamp: tradeTime.toISOString(),
          price,
          volume,
          amount: Number((price * volume).toFixed(2)),
          direction,
          type: 'market'
        })
      }

      return {
        symbol,
        trades,
        count: trades.length,
        timestamp: new Date().toISOString()
      }
    }

    try {
      const response = await httpClient.get<any>(`${API_BASE}/quotes/${symbol}/trades`, {
        params: { limit }
      })

      if (response.data?.success && response.data?.data) {
        return response.data.data
      }

      return response.data || null
    } catch (error) {
      console.warn(`获取股票${symbol}逐笔成交失败，使用模拟数据:`, error)
      // 返回模拟数据
      const trades = []
      const basePrice = 10.0 + Math.random() * 2
      const now = new Date()

      for (let i = 0; i < limit; i++) {
        const tradeTime = new Date(now.getTime() - i * Math.random() * 10000)
        const price = Number((basePrice + (Math.random() - 0.5) * 0.1).toFixed(2))
        const volume = [100, 200, 300, 500, 1000][Math.floor(Math.random() * 5)]
        const direction = ['buy', 'sell', 'neutral'][Math.floor(Math.random() * 3)]

        trades.push({
          time: tradeTime.toTimeString().slice(0, 8),
          timestamp: tradeTime.toISOString(),
          price,
          volume,
          amount: Number((price * volume).toFixed(2)),
          direction,
          type: 'market'
        })
      }

      return {
        symbol,
        trades,
        count: trades.length,
        timestamp: new Date().toISOString()
      }
    }
  }

  /**
   * 搜索股票
   */
  async searchStocks(params: SearchParams): Promise<StockSearchResult[]> {
    if (USE_MOCK) {
      const mockStocks = await mockService.getQuoteData(['000001', '000002', '000858', '600036', '600519'])
      return mockStocks
        .filter(stock =>
          stock.symbol.includes(params.keyword) ||
          stock.name.includes(params.keyword)
        )
        .slice(0, params.limit || 10)
        .map(stock => ({
          symbol: stock.symbol,
          name: stock.name,
          currentPrice: stock.currentPrice,
          changePercent: stock.changePercent,
          market: stock.symbol.startsWith('6') ? 'SH' : 'SZ'
        }))
    }

    try {
      const response = await httpClient.get<any>(`${API_BASE}/search`, {
        params: {
          keyword: params.keyword,
          limit: params.limit || 20
        }
      })

      if (response.data?.success && response.data?.data?.results) {
        return response.data.data.results
      }

      return response.data || []
    } catch (error) {
      console.warn('搜索股票失败，使用模拟数据:', error)
      // 使用模拟数据作为备选
      const mockStocks = await mockService.getQuoteData(['000001', '000002', '000858', '600036', '600519'])
      return mockStocks
        .filter(stock =>
          stock.symbol.includes(params.keyword) ||
          stock.name.includes(params.keyword)
        )
        .slice(0, params.limit || 10)
        .map(stock => ({
          symbol: stock.symbol,
          name: stock.name,
          currentPrice: stock.currentPrice,
          changePercent: stock.changePercent,
          market: stock.symbol.startsWith('6') ? 'SH' : 'SZ'
        }))
    }
  }

  /**
   * 获取市场概览
   */
  async getMarketOverview(): Promise<any> {
    if (USE_MOCK) {
      return mockService.getMarketOverview()
    }

    try {
      const response = await httpClient.get<any>(`${API_BASE}/overview`)

      if (response.data?.success && response.data?.data) {
        return response.data.data
      }

      return response.data || {}
    } catch (error) {
      console.warn('市场概览API调用失败，使用模拟数据:', error)
      return mockService.getMarketOverview()
    }
  }

  /**
   * 获取股票列表
   */
  async getStockList(params: {
    market?: string
    industry?: string
    page?: number
    pageSize?: number
  } = {}): Promise<QuoteData[]> {
    if (USE_MOCK) {
      const mockStocks = await mockService.getQuoteData([
        '000001', '000002', '000858', '600036', '600519',
        '000063', '002415', '300059', '600000', '000166',
        '002594', '600276', '000725', '002304', '600887'
      ])
      return mockStocks
    }

    try {
      const response = await httpClient.get<any>(`${API_BASE}/stocks`, {
        params: {
          market: params.market,
          industry: params.industry,
          page: params.page || 1,
          pageSize: params.pageSize || 20
        }
      })

      if (response.data?.success && response.data?.data) {
        return response.data.data
      }

      return response.data || []
    } catch (error) {
      console.warn('获取股票列表失败，使用模拟数据:', error)
      const mockStocks = await mockService.getQuoteData([
        '000001', '000002', '000858', '600036', '600519',
        '000063', '002415', '300059', '600000', '000166'
      ])
      return mockStocks
    }
  }

  /**
   * 获取历史数据股票列表
   */
  async getHistoricalStockList(params: {
    market?: string
    industry?: string
    page?: number
    page_size?: number
  } = {}): Promise<any> {
    try {
      const response = await httpClient.get<any>(`${API_BASE}/historical/stocks`, {
        params
      })

      if (response.data?.success && response.data?.data) {
        return response.data.data
      }

      return response.data || []
    } catch (error) {
      console.warn('获取历史数据股票列表失败:', error)
      return { stocks: [], total: 0, total_pages: 0 }
    }
  }

  /**
   * 获取历史数据
   */
  async getHistoricalStockData(
    symbol: string,
    params: {
      start_date?: string
      end_date?: string
      columns?: string[]
    } = {}
  ): Promise<any> {
    try {
      const response = await httpClient.get<any>(`${API_BASE}/historical/data/${symbol}`, {
        params: {
          start_date: params.start_date,
          end_date: params.end_date,
          columns: params.columns?.join(',')
        }
      })

      if (response.data?.success && response.data?.data) {
        return response.data.data
      }

      return response.data || []
    } catch (error) {
      console.warn(`获取${symbol}历史数据失败:`, error)
      return []
    }
  }

  /**
   * 搜索历史数据股票
   */
  async searchHistoricalStocks(keyword: string, limit: number = 20): Promise<any> {
    try {
      const response = await httpClient.get<any>(`${API_BASE}/historical/search`, {
        params: { keyword, limit }
      })

      if (response.data?.success && response.data?.data) {
        return response.data.data
      }

      return response.data || []
    } catch (error) {
      console.warn('搜索历史数据股票失败:', error)
      return []
    }
  }

  /**
   * 获取市场深度
   */
  async getMarketDepth(symbol: string): Promise<DepthData | null> {
    try {
      const response = await httpClient.get<any>(`${API_BASE}/depth/${symbol}`)

      if (response.data?.success && response.data?.data) {
        return response.data.data
      }

      return response.data || null
    } catch (error) {
      console.warn(`获取${symbol}市场深度失败:`, error)
      return null
    }
  }

  /**
   * 清除缓存
   */
  async clearCache(pattern: string = '*'): Promise<boolean> {
    try {
      const response = await httpClient.post<any>(`${API_BASE}/cache/clear`, null, {
        params: { pattern }
      })

      return response.data?.success || false
    } catch (error) {
      console.warn('清除缓存失败:', error)
      return false
    }
  }

  /**
   * 健康检查
   */
  async healthCheck(): Promise<any> {
    try {
      const response = await httpClient.get<any>(`${API_BASE}/health`)
      return response.data || { status: 'unknown' }
    } catch (error) {
      console.warn('健康检查失败:', error)
      return { status: 'error', error: error.message }
    }
  }

  // 兼容性方法，保持向后兼容

  /**
   * 获取指定市场概览
   */
  async getMarketOverviewByMarket(market: string): Promise<MarketOverview> {
    const overview = await this.getMarketOverview()
    return overview // 简化实现
  }

  /**
   * 获取板块数据
   */
  async getSectors(): Promise<SectorData[]> {
    try {
      const response = await httpClient.get<any>(`${API_BASE}/sectors`)
      return response.data?.data || []
    } catch (error) {
      console.warn('获取板块数据失败:', error)
      return []
    }
  }

  /**
   * 获取指数数据
   */
  async getIndices(): Promise<IndexData[]> {
    try {
      const response = await httpClient.get<any>(`${API_BASE}/indices`)
      return response.data?.data || []
    } catch (error) {
      console.warn('获取指数数据失败:', error)
      return []
    }
  }

  /**
   * 获取新闻资讯
   */
  async getNews(params?: {
    category?: string
    limit?: number
    offset?: number
    symbol?: string
  }): Promise<NewsData[]> {
    try {
      const response = await httpClient.get<any>(`${API_BASE}/news`, { params })
      return response.data?.data || []
    } catch (error) {
      console.warn('获取新闻失败:', error)
      return []
    }
  }

  /**
   * 获取排行榜数据
   */
  async getRanking(type: RankingType, params?: {
    market?: string
    limit?: number
    direction?: 'asc' | 'desc'
  }): Promise<RankingData[]> {
    try {
      const response = await httpClient.get<any>(`${API_BASE}/ranking`, {
        params: { type, ...params }
      })
      return response.data?.data || []
    } catch (error) {
      console.warn('获取排行榜失败:', error)
      return []
    }
  }



  /**
   * 获取自选股列表
   */
  async getWatchlist(): Promise<WatchlistItem[]> {
    if (USE_MOCK) return []

    try {
      const response = await httpClient.get<any>(`${API_BASE}/watchlist`)
      return response.data?.data || []
    } catch (error) {
      console.warn('获取自选股失败:', error)
      return []
    }
  }

  /**
   * 添加自选股
   */
  async addToWatchlist(symbol: string): Promise<void> {
    if (USE_MOCK) return

    await httpClient.post(`${API_BASE}/watchlist`, { symbol })
  }

  /**
   * 移除自选股
   */
  async removeFromWatchlist(symbol: string): Promise<void> {
    if (USE_MOCK) return

    await httpClient.delete(`${API_BASE}/watchlist/${symbol}`)
  }
}

// 创建统一实例
export const unifiedMarketApi = new UnifiedMarketAPI()

// 导出别名以保持兼容性
export const marketApi = unifiedMarketApi

// 导出默认实例
export default unifiedMarketApi
