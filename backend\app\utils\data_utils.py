"""
数据处理工具函数
提供通用的数据验证、转换和处理功能
"""

import re
from datetime import datetime, timedelta
from decimal import Decimal, InvalidOperation
from typing import Any, Optional, Union


def validate_date_format(date_string: str) -> bool:
    """验证日期格式是否为YYYY-MM-DD"""
    if not date_string:
        return False
    
    pattern = r'^\d{4}-\d{2}-\d{2}$'
    if not re.match(pattern, date_string):
        return False
    
    try:
        datetime.strptime(date_string, "%Y-%m-%d")
        return True
    except ValueError:
        return False


def safe_decimal_convert(value: Any, default: Optional[Decimal] = None) -> Optional[Decimal]:
    """安全地将值转换为Decimal类型"""
    if value is None:
        return default
    
    try:
        if isinstance(value, (int, float)):
            return Decimal(str(value))
        elif isinstance(value, str):
            # 移除可能的空格和货币符号
            cleaned_value = value.strip().replace('¥', '').replace(',', '')
            return Decimal(cleaned_value)
        elif isinstance(value, Decimal):
            return value
        else:
            return default
    except (InvalidOperation, ValueError):
        return default


def safe_float_convert(value: Any, default: float = 0.0) -> float:
    """安全地将值转换为float类型"""
    if value is None:
        return default
    
    try:
        if isinstance(value, (int, float)):
            return float(value)
        elif isinstance(value, str):
            cleaned_value = value.strip().replace('¥', '').replace(',', '')
            return float(cleaned_value)
        elif isinstance(value, Decimal):
            return float(value)
        else:
            return default
    except (ValueError, TypeError):
        return default


def safe_int_convert(value: Any, default: int = 0) -> int:
    """安全地将值转换为int类型"""
    if value is None:
        return default
    
    try:
        if isinstance(value, int):
            return value
        elif isinstance(value, (float, Decimal)):
            return int(value)
        elif isinstance(value, str):
            cleaned_value = value.strip().replace(',', '')
            return int(float(cleaned_value))
        else:
            return default
    except (ValueError, TypeError):
        return default


def format_number(value: Union[int, float, Decimal], precision: int = 2) -> str:
    """格式化数字显示"""
    if value is None:
        return "0.00"
    
    try:
        if isinstance(value, (int, float, Decimal)):
            return f"{float(value):,.{precision}f}"
        else:
            return str(value)
    except (ValueError, TypeError):
        return "0.00"


def format_percentage(value: Union[int, float, Decimal], precision: int = 2) -> str:
    """格式化百分比显示"""
    if value is None:
        return "0.00%"
    
    try:
        return f"{float(value):.{precision}f}%"
    except (ValueError, TypeError):
        return "0.00%"


def validate_stock_symbol(symbol: str) -> bool:
    """验证股票代码格式"""
    if not symbol:
        return False
    
    # A股股票代码格式：6位数字
    pattern = r'^\d{6}$'
    return bool(re.match(pattern, symbol))


def normalize_stock_symbol(symbol: str) -> str:
    """标准化股票代码"""
    if not symbol:
        return ""
    
    # 移除可能的前缀和后缀
    symbol = symbol.upper().strip()
    symbol = re.sub(r'[^0-9]', '', symbol)  # 只保留数字
    
    # 确保是6位数字
    if len(symbol) == 6 and symbol.isdigit():
        return symbol
    
    return symbol


def calculate_change_rate(current: Union[int, float], previous: Union[int, float]) -> Optional[float]:
    """计算涨跌幅"""
    if current is None or previous is None or previous == 0:
        return None
    
    try:
        return ((float(current) - float(previous)) / float(previous)) * 100
    except (ValueError, TypeError, ZeroDivisionError):
        return None


def calculate_ma(data: list, period: int) -> list:
    """计算移动平均线"""
    if not data or period <= 0 or len(data) < period:
        return []
    
    ma_values = []
    for i in range(len(data)):
        if i < period - 1:
            ma_values.append(None)
        else:
            period_data = data[i - period + 1:i + 1]
            avg = sum(period_data) / period
            ma_values.append(round(avg, 2))
    
    return ma_values


def is_trading_day(date: datetime) -> bool:
    """判断是否为交易日（简单实现，不考虑节假日）"""
    return date.weekday() < 5  # 周一到周五


def get_trading_days_between(start_date: datetime, end_date: datetime) -> int:
    """计算两个日期之间的交易日数量"""
    if start_date > end_date:
        return 0
    
    trading_days = 0
    current_date = start_date
    
    while current_date <= end_date:
        if is_trading_day(current_date):
            trading_days += 1
        current_date += timedelta(days=1)
    
    return trading_days


def sanitize_filename(filename: str) -> str:
    """清理文件名，移除不安全字符"""
    if not filename:
        return "unnamed"
    
    # 移除或替换不安全字符
    unsafe_chars = r'[<>:"/\\|?*]'
    sanitized = re.sub(unsafe_chars, '_', filename)
    
    # 移除连续的下划线
    sanitized = re.sub(r'_{2,}', '_', sanitized)
    
    # 移除开头和结尾的下划线和空格
    sanitized = sanitized.strip('_ ')
    
    return sanitized if sanitized else "unnamed"


def chunk_list(lst: list, chunk_size: int) -> list:
    """将列表分块处理"""
    if chunk_size <= 0:
        return []
    
    return [lst[i:i + chunk_size] for i in range(0, len(lst), chunk_size)]