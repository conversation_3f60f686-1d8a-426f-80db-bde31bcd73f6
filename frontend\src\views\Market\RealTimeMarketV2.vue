<template>
  <div class="market-realtime-container">
    <!-- 市场指数 -->
    <div class="market-indices">
      <div v-for="index in indices" :key="index.code" class="index-item">
        <div class="index-name">{{ index.name }}</div>
        <div class="index-value" :class="index.change >= 0 ? 'up' : 'down'">
          {{ index.value.toFixed(2) }}
        </div>
        <div class="index-change" :class="index.change >= 0 ? 'up' : 'down'">
          {{ index.change >= 0 ? '+' : '' }}{{ index.change.toFixed(2) }}
          ({{ index.changePercent >= 0 ? '+' : '' }}{{ index.changePercent.toFixed(2) }}%)
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧股票列表 -->
      <div class="stock-list">
        <div class="list-header">
          <el-input
            v-model="searchText"
            placeholder="搜索股票代码/名称"
            size="small"
            clearable
            @input="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>

          <div class="quick-filters">
            <el-button
              v-for="filter in quickFilters"
              :key="filter.value"
              :type="activeFilter === filter.value ? 'primary' : ''"
              size="small"
              @click="activeFilter = filter.value"
            >
              {{ filter.label }}
            </el-button>
          </div>
        </div>

        <div class="stock-table">
          <el-table
            :data="filteredStocks.slice(0, 50)"
            size="small"
            height="600"
            @row-click="handleStockSelect"
            highlight-current-row
          >
            <el-table-column prop="symbol" label="代码" width="80" />
            <el-table-column prop="name" label="名称" width="100" />
            <el-table-column prop="price" label="最新价" width="80">
              <template #default="{ row }">
                <span :class="row.changePercent >= 0 ? 'up' : 'down'">
                  {{ row.price?.toFixed(2) }}
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="changePercent" label="涨跌幅" width="80">
              <template #default="{ row }">
                <span :class="row.changePercent >= 0 ? 'up' : 'down'">
                  {{ row.changePercent >= 0 ? '+' : '' }}{{ row.changePercent?.toFixed(2) }}%
                </span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="60">
              <template #default="{ row }">
                <el-button
                  v-if="!isFavorite(row.symbol)"
                  size="small"
                  type="text"
                  @click.stop="addToFavorites(row)"
                >
                  ⭐
                </el-button>
                <el-button
                  v-else
                  size="small"
                  type="text"
                  @click.stop="removeFromFavorites(row.symbol)"
                >
                  ★
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <!-- 中间图表区域 -->
      <div class="chart-area">
        <div class="chart-header">
          <div class="stock-info">
            <h2>{{ selectedStock.name }} ({{ selectedStock.symbol }})</h2>
            <div class="price-info">
              <span class="current-price" :class="selectedStock.changePercent >= 0 ? 'up' : 'down'">
                {{ selectedStock.price?.toFixed(2) }}
              </span>
              <span class="price-change" :class="selectedStock.changePercent >= 0 ? 'up' : 'down'">
                {{ selectedStock.changePercent >= 0 ? '+' : '' }}{{ selectedStock.change?.toFixed(2) }}
                ({{ selectedStock.changePercent >= 0 ? '+' : '' }}{{ selectedStock.changePercent?.toFixed(2) }}%)
              </span>
            </div>
          </div>

          <div class="chart-controls">
            <el-radio-group v-model="chartType" size="small">
              <el-radio-button label="time">分时</el-radio-button>
              <el-radio-button label="1d">日K</el-radio-button>
              <el-radio-button label="1w">周K</el-radio-button>
              <el-radio-button label="1M">月K</el-radio-button>
            </el-radio-group>

            <el-select v-model="selectedIndicator" size="small" style="width: 100px; margin-left: 12px;">
              <el-option label="无指标" value="none" />
              <el-option label="MACD" value="macd" />
              <el-option label="KDJ" value="kdj" />
              <el-option label="RSI" value="rsi" />
            </el-select>
          </div>
        </div>

        <div ref="chartContainer" class="kline-chart"></div>

        <!-- 关键指标 -->
        <div class="key-metrics">
          <div class="metric-item">
            <span class="metric-label">今开</span>
            <span class="metric-value">{{ selectedStock.open?.toFixed(2) }}</span>
          </div>
          <div class="metric-item">
            <span class="metric-label">昨收</span>
            <span class="metric-value">{{ selectedStock.preClose?.toFixed(2) }}</span>
          </div>
          <div class="metric-item">
            <span class="metric-label">最高</span>
            <span class="metric-value up">{{ selectedStock.high?.toFixed(2) }}</span>
          </div>
          <div class="metric-item">
            <span class="metric-label">最低</span>
            <span class="metric-value down">{{ selectedStock.low?.toFixed(2) }}</span>
          </div>
          <div class="metric-item">
            <span class="metric-label">成交量</span>
            <span class="metric-value">{{ formatVolume(selectedStock.volume) }}</span>
          </div>
          <div class="metric-item">
            <span class="metric-label">成交额</span>
            <span class="metric-value">{{ formatAmount(selectedStock.amount) }}万</span>
          </div>
        </div>
      </div>

      <!-- 右侧交易面板 -->
      <div class="trade-panel">
        <!-- 五档盘口 -->
        <div class="order-book">
          <h3>五档盘口</h3>
          <div class="order-list">
            <!-- 卖五档 -->
            <div class="sell-orders">
              <div v-for="(order, index) in orderBook.ask" :key="`ask-${index}`" class="order-row">
                <span class="order-level">卖{{ order.level }}</span>
                <span class="order-price down">{{ order.price?.toFixed(2) }}</span>
                <span class="order-volume">{{ Math.floor(order.volume / 100) }}</span>
              </div>
            </div>

            <!-- 现价 -->
            <div class="market-price">
              <span class="current-price" :class="selectedStock.changePercent >= 0 ? 'up' : 'down'">
                {{ selectedStock.price?.toFixed(2) }}
              </span>
            </div>

            <!-- 买五档 -->
            <div class="buy-orders">
              <div v-for="(order, index) in orderBook.bid" :key="`bid-${index}`" class="order-row">
                <span class="order-level">买{{ order.level }}</span>
                <span class="order-price up">{{ order.price?.toFixed(2) }}</span>
                <span class="order-volume">{{ Math.floor(order.volume / 100) }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 快速交易 -->
        <div class="quick-trade">
          <h3>快速交易</h3>
          <div class="trade-form">
            <el-radio-group v-model="tradeDirection" size="small">
              <el-radio-button label="buy" class="buy-btn">买入</el-radio-button>
              <el-radio-button label="sell" class="sell-btn">卖出</el-radio-button>
            </el-radio-group>

            <div class="price-input">
              <el-input
                v-model="tradePrice"
                placeholder="价格"
                size="small"
                type="number"
                step="0.01"
              />
              <el-button size="small" @click="setMarketPrice">市价</el-button>
            </div>

            <el-input
              v-model="tradeQuantity"
              placeholder="数量(手)"
              size="small"
              type="number"
              step="100"
            />

            <div class="quick-quantity">
              <el-button
                v-for="qty in [100, 200, 500, 1000]"
                :key="qty"
                size="small"
                @click="tradeQuantity = qty"
              >
                {{ qty }}手
              </el-button>
            </div>

            <el-button
              :type="tradeDirection === 'buy' ? 'danger' : 'success'"
              size="small"
              style="width: 100%"
              @click="submitTrade"
            >
              {{ tradeDirection === 'buy' ? '买入' : '卖出' }}
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch, nextTick } from 'vue'
import { Search } from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import { marketApi } from '@/api/market_unified'

// 响应式数据
const searchText = ref('')
const activeFilter = ref('all')
const selectedStock = ref<any>({})
const chartType = ref('time')
const selectedIndicator = ref('none')
const chartInstance = ref<any>(null)
const chartContainer = ref<HTMLElement>()
const tradeDirection = ref('buy')
const tradePrice = ref('')
const tradeQuantity = ref(100)
const favorites = ref(JSON.parse(localStorage.getItem('favorites') || '[]'))
const stocks = ref<any[]>([])
const orderBook = ref<any>({ bid: [], ask: [] })

// 市场指数数据
const indices = ref([
  { name: '上证指数', code: 'SH000001', value: 3250.12, change: 12.34, changePercent: 0.38 },
  { name: '深证成指', code: 'SZ399001', value: 11725.45, change: -23.56, changePercent: -0.20 },
  { name: '创业板指', code: 'SZ399006', value: 2405.67, change: 8.91, changePercent: 0.37 },
  { name: '沪深300', code: 'SH000300', value: 3987.23, change: 15.78, changePercent: 0.40 }
])

// 快速筛选选项
const quickFilters = ref([
  { label: '全部', value: 'all' },
  { label: '自选', value: 'favorites' },
  { label: '涨幅榜', value: 'gainers' },
  { label: '跌幅榜', value: 'losers' },
  { label: '成交额', value: 'amount' }
])

// 初始化
onMounted(async () => {
  await loadStocks()
  if (stocks.value.length > 0) {
    selectedStock.value = stocks.value[0]
    await loadOrderBook()
    await nextTick()
    initChart()
  }
})

// 监听股票选择变化
watch(selectedStock, async (newVal) => {
  if (newVal && newVal.symbol) {
    await loadOrderBook()
    if (chartInstance.value) {
      updateChart()
    }
  }
})

// 监听图表类型变化
watch(chartType, () => {
  if (chartInstance.value) {
    updateChart()
  }
})

// 加载股票列表
const loadStocks = async () => {
  try {
    const symbols = ['000001', '000002', '000858', '600036', '600519', '600000', '000166', '002415', '300059', '002594']
    const quotes = await marketApi.getQuotes({ symbols })
    stocks.value = quotes
  } catch (error) {
    console.error('加载股票列表失败:', error)
  }
}

// 加载五档盘口
const loadOrderBook = async () => {
  if (!selectedStock.value?.symbol) return

  try {
    const data = await marketApi.getOrderBook(selectedStock.value.symbol)
    orderBook.value = data || { bid: [], ask: [] }
  } catch (error) {
    console.error('加载五档盘口失败:', error)
  }
}

// 初始化图表
const initChart = () => {
  if (!chartContainer.value) return

  chartInstance.value = echarts.init(chartContainer.value)
  updateChart()
}

// 更新图表
const updateChart = () => {
  if (!chartInstance.value || !selectedStock.value) return

  const option = {
    title: {
      text: `${selectedStock.value.name} (${selectedStock.value.symbol})`,
      left: 'center',
      textStyle: { fontSize: 16 }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'cross' }
    },
    legend: {
      data: ['日K', 'MA5', 'MA10', 'MA20', '成交量'],
      bottom: 10
    },
    grid: [
      { left: '10%', right: '10%', height: '50%' },
      { left: '10%', right: '10%', top: '63%', height: '16%' }
    ],
    xAxis: [
      {
        type: 'category',
        data: generateTimeData(),
        scale: true,
        boundaryGap: false,
        axisLine: { onZero: false },
        splitLine: { show: false }
      },
      {
        type: 'category',
        gridIndex: 1,
        data: generateTimeData(),
        scale: true,
        boundaryGap: false,
        axisLine: { onZero: false },
        axisTick: { show: false },
        splitLine: { show: false },
        axisLabel: { show: false }
      }
    ],
    yAxis: [
      { scale: true, splitArea: { show: true } },
      {
        scale: true,
        gridIndex: 1,
        splitNumber: 2,
        axisLabel: { show: false },
        axisLine: { show: false },
        axisTick: { show: false },
        splitLine: { show: false }
      }
    ],
    dataZoom: [
      { type: 'inside', xAxisIndex: [0, 1], start: 50, end: 100 }
    ],
    series: [
      {
        name: '日K',
        type: 'candlestick',
        data: generateKLineData(),
        itemStyle: {
          color: '#ec0000',
          color0: '#00da3c',
          borderColor: '#8A0000',
          borderColor0: '#008F28'
        }
      },
      {
        name: 'MA5',
        type: 'line',
        data: generateMAData(5),
        smooth: true,
        lineStyle: { width: 1, color: '#1890ff' }
      },
      {
        name: 'MA10',
        type: 'line',
        data: generateMAData(10),
        smooth: true,
        lineStyle: { width: 1, color: '#52c41a' }
      },
      {
        name: 'MA20',
        type: 'line',
        data: generateMAData(20),
        smooth: true,
        lineStyle: { width: 1, color: '#faad14' }
      },
      {
        name: '成交量',
        type: 'bar',
        xAxisIndex: 1,
        yAxisIndex: 1,
        data: generateVolumeData()
      }
    ]
  }

  chartInstance.value.setOption(option)
}

// 生成模拟数据
const generateTimeData = () => {
  const data = []
  const now = new Date()
  for (let i = 29; i >= 0; i--) {
    const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000)
    data.push(date.toLocaleDateString())
  }
  return data
}

const generateKLineData = () => {
  const data = []
  let price = selectedStock.value.price || 10
  for (let i = 0; i < 30; i++) {
    const open = price + (Math.random() - 0.5) * 0.5
    const close = open + (Math.random() - 0.5) * 0.8
    const high = Math.max(open, close) + Math.random() * 0.3
    const low = Math.min(open, close) - Math.random() * 0.3
    data.push([open, close, low, high])
    price = close
  }
  return data
}

const generateMAData = (period: number) => {
  const data = []
  let price = selectedStock.value.price || 10
  for (let i = 0; i < 30; i++) {
    price += (Math.random() - 0.5) * 0.2
    data.push(Number(price.toFixed(2)))
  }
  return data
}

const generateVolumeData = () => {
  const data = []
  for (let i = 0; i < 30; i++) {
    data.push(Math.floor(Math.random() * 10000) + 1000)
  }
  return data
}

// 处理股票选择
const handleStockSelect = (stock: any) => {
  selectedStock.value = stock
}

// 处理搜索
const handleSearch = () => {
  // 搜索逻辑已在计算属性中处理
}

// 添加到自选
const addToFavorites = (stock: any) => {
  if (!favorites.value.some((fav: any) => fav.symbol === stock.symbol)) {
    favorites.value.push(stock)
    localStorage.setItem('favorites', JSON.stringify(favorites.value))
  }
}

// 从自选移除
const removeFromFavorites = (symbol: string) => {
  favorites.value = favorites.value.filter((fav: any) => fav.symbol !== symbol)
  localStorage.setItem('favorites', JSON.stringify(favorites.value))
}

// 检查是否在自选
const isFavorite = (symbol: string) => {
  return favorites.value.some((fav: any) => fav.symbol === symbol)
}

// 设置交易价格为最新价
const setMarketPrice = () => {
  tradePrice.value = selectedStock.value.price?.toString() || ''
}

// 格式化成交量
const formatVolume = (volume: number) => {
  if (!volume) return '0'
  if (volume >= 10000) {
    return (volume / 10000).toFixed(2) + '万手'
  }
  return Math.floor(volume / 100) + '手'
}

// 格式化成交额
const formatAmount = (amount: number) => {
  if (!amount) return '0'
  if (amount >= 10000) {
    return (amount / 10000).toFixed(2)
  }
  return amount.toFixed(2)
}

// 提交交易
const submitTrade = () => {
  console.log(`提交交易: ${tradeDirection.value} ${tradeQuantity.value}手 ${selectedStock.value.symbol} @ ${tradePrice.value}`)
  // 这里应该调用实际的交易API
}

// 计算过滤后的股票列表
const filteredStocks = computed(() => {
  let result = [...stocks.value]

  // 根据搜索文本过滤
  if (searchText.value) {
    result = result.filter(stock =>
      stock.symbol.includes(searchText.value) ||
      stock.name.includes(searchText.value)
    )
  }

  // 根据快速筛选过滤
  if (activeFilter.value === 'favorites') {
    result = result.filter(stock => favorites.value.some((fav: any) => fav.symbol === stock.symbol))
  } else if (activeFilter.value === 'gainers') {
    result = [...result].sort((a, b) => (b.changePercent || 0) - (a.changePercent || 0))
  } else if (activeFilter.value === 'losers') {
    result = [...result].sort((a, b) => (a.changePercent || 0) - (b.changePercent || 0))
  } else if (activeFilter.value === 'amount') {
    result = [...result].sort((a, b) => (b.amount || 0) - (a.amount || 0))
  }

  return result
})
</script>

<style scoped>
.market-realtime-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  padding: 16px;
  background-color: #f5f7fa;
  font-family: 'Microsoft YaHei', sans-serif;
}

.market-indices {
  display: flex;
  justify-content: space-around;
  background: #fff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.index-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 120px;
}

.index-name {
  font-size: 14px;
  color: #606266;
  margin-bottom: 4px;
}

.index-value {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 4px;
}

.index-change {
  font-size: 12px;
}

.up {
  color: #f56c6c; /* 红色表示上涨 */
}

.down {
  color: #67c23a; /* 绿色表示下跌 */
}

.main-content {
  display: flex;
  flex: 1;
  gap: 16px;
  min-height: 0;
}

.stock-list {
  width: 320px;
  background: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
}

.list-header {
  margin-bottom: 16px;
}

.quick-filters {
  display: flex;
  gap: 8px;
  margin-top: 12px;
  flex-wrap: wrap;
}

.stock-table {
  flex: 1;
  overflow: hidden;
}

.chart-area {
  flex: 1;
  background: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  min-width: 0;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
  flex-wrap: wrap;
  gap: 16px;
}

.stock-info h2 {
  margin: 0 0 8px 0;
  font-size: 20px;
  color: #303133;
}

.price-info {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.current-price {
  font-size: 28px;
  font-weight: bold;
}

.price-change {
  font-size: 16px;
  font-weight: 500;
}

.chart-controls {
  display: flex;
  gap: 12px;
  align-items: center;
  flex-wrap: wrap;
}

.kline-chart {
  flex: 1;
  min-height: 400px;
  width: 100%;
}

.key-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 12px;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #ebeef5;
}

.metric-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f5f7fa;
  border-radius: 4px;
}

.metric-label {
  color: #909399;
  font-size: 14px;
}

.metric-value {
  font-weight: 500;
  font-size: 14px;
  color: #303133;
}

.trade-panel {
  width: 300px;
  background: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.trade-panel h3 {
  margin: 0 0 12px 0;
  font-size: 16px;
  color: #303133;
  border-bottom: 2px solid #409eff;
  padding-bottom: 8px;
}

.order-book {
  display: flex;
  flex-direction: column;
}

.order-list {
  display: flex;
  flex-direction: column;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  overflow: hidden;
}

.sell-orders .order-row {
  background: linear-gradient(to left, rgba(103, 194, 58, 0.1), transparent);
}

.buy-orders .order-row {
  background: linear-gradient(to right, rgba(245, 108, 108, 0.1), transparent);
}

.order-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 12px;
  font-size: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.order-row:last-child {
  border-bottom: none;
}

.order-level {
  width: 40px;
  color: #909399;
  font-size: 11px;
}

.order-price {
  width: 60px;
  text-align: center;
  font-weight: 500;
  font-size: 12px;
}

.order-volume {
  width: 50px;
  text-align: right;
  color: #606266;
  font-size: 11px;
}

.market-price {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 12px;
  background: #f8f9fa;
  border-top: 1px solid #ebeef5;
  border-bottom: 1px solid #ebeef5;
  font-weight: bold;
}

.quick-trade {
  display: flex;
  flex-direction: column;
}

.trade-form {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.price-input {
  display: flex;
  gap: 8px;
}

.quick-quantity {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.quick-quantity .el-button {
  flex: 1;
  min-width: 60px;
}

.buy-btn {
  color: #f56c6c !important;
}

.sell-btn {
  color: #67c23a !important;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .main-content {
    flex-direction: column;
  }

  .stock-list {
    width: 100%;
    height: 300px;
  }

  .trade-panel {
    width: 100%;
  }

  .chart-header {
    flex-direction: column;
    align-items: flex-start;
  }
}

@media (max-width: 768px) {
  .market-indices {
    flex-wrap: wrap;
    gap: 12px;
  }

  .index-item {
    min-width: 100px;
  }

  .key-metrics {
    grid-template-columns: repeat(2, 1fr);
  }

  .price-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}
</style>
