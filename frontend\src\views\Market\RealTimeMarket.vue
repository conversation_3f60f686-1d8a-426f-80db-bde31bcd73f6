<template>
  <div class="realtime-market">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1>实时行情中心</h1>
      <p>实时股票价格监控与分析系统</p>
    </div>

    <!-- 实时指标卡片 -->
    <el-row :gutter="20" class="stats-cards">
      <el-col :span="6">
        <el-card class="stat-card realtime">
          <div class="stat-content">
            <div class="stat-number">{{ formatNumber(realtimeStats.activeStocks || 0) }}</div>
            <div class="stat-label">活跃股票</div>
            <div class="stat-status online">
              <el-icon><VideoPlay /></el-icon>
              实时更新中
            </div>
          </div>
          <el-icon class="stat-icon"><TrendCharts /></el-icon>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ formatNumber(realtimeStats.totalVolume || 0) }}</div>
            <div class="stat-label">总成交量</div>
            <div class="stat-detail">{{ formatNumber(realtimeStats.totalAmount || 0) }}万元</div>
          </div>
          <el-icon class="stat-icon"><Money /></el-icon>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ realtimeStats.upCount || 0 }}</div>
            <div class="stat-label">上涨股票</div>
            <div class="stat-detail">{{ realtimeStats.downCount || 0 }}只下跌</div>
          </div>
          <el-icon class="stat-icon up"><ArrowUp /></el-icon>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ getCurrentTime() }}</div>
            <div class="stat-label">更新时间</div>
            <div class="stat-detail">{{ connectionStatus }}</div>
          </div>
          <el-icon class="stat-icon" :class="{ 'online': isConnected }"><Connection /></el-icon>
        </el-card>
      </el-col>
    </el-row>

    <!-- 搜索和筛选 -->
    <el-card class="search-card">
      <el-row :gutter="20" align="middle">
        <el-col :span="8">
          <el-input
            v-model="searchQuery"
            placeholder="搜索股票代码或名称"
            clearable
            @input="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-col>
        <el-col :span="4">
          <el-select v-model="selectedMarket" placeholder="选择市场" @change="handleMarketChange">
            <el-option label="全部市场" value="" />
            <el-option label="上海证券交易所" value="SH" />
            <el-option label="深圳证券交易所" value="SZ" />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-select v-model="selectedSector" placeholder="选择行业" @change="handleSectorChange">
            <el-option label="全部行业" value="" />
            <el-option v-for="sector in sectors" :key="sector" :label="sector" :value="sector" />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-select v-model="sortBy" placeholder="排序方式" @change="handleSort">
            <el-option label="涨跌幅" value="change_percent" />
            <el-option label="成交量" value="volume" />
            <el-option label="成交额" value="amount" />
            <el-option label="股票代码" value="code" />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-button-group>
            <el-button
              :type="isConnected ? 'danger' : 'primary'"
              @click="toggleConnection"
              :loading="connecting"
            >
              <el-icon><VideoPlay v-if="!isConnected" /><VideoPause v-else /></el-icon>
              {{ isConnected ? '停止' : '开始' }}
            </el-button>
            <el-button @click="refreshData">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </el-button-group>
        </el-col>
      </el-row>
    </el-card>

    <!-- 实时股票列表 -->
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span>实时股票行情</span>
          <div class="header-actions">
            <el-tag :type="isConnected ? 'success' : 'danger'" size="small">
              {{ isConnected ? '实时连接' : '连接断开' }}
            </el-tag>
            <span class="update-time">最后更新: {{ lastUpdateTime }}</span>
          </div>
        </div>
      </template>

      <el-table
        :data="filteredStocks"
        v-loading="loading"
        stripe
        height="600"
        @row-click="handleRowClick"
        class="realtime-table"
      >
        <el-table-column prop="code" label="股票代码" width="100" fixed="left">
          <template #default="{ row }">
            <span class="stock-code">{{ row.code }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="name" label="股票名称" width="120" fixed="left">
          <template #default="{ row }">
            <span class="stock-name">{{ row.name }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="current_price" label="现价" width="100" align="right">
          <template #default="{ row }">
            <span :class="getPriceClass(row.change_percent)">
              ¥{{ formatPrice(row.current_price) }}
            </span>
          </template>
        </el-table-column>

        <el-table-column prop="change_amount" label="涨跌额" width="100" align="right">
          <template #default="{ row }">
            <span :class="getPriceClass(row.change_percent)">
              {{ formatChange(row.change_amount) }}
            </span>
          </template>
        </el-table-column>

        <el-table-column prop="change_percent" label="涨跌幅" width="100" align="right">
          <template #default="{ row }">
            <span :class="getPriceClass(row.change_percent)">
              {{ formatPercent(row.change_percent) }}%
            </span>
          </template>
        </el-table-column>

        <el-table-column prop="volume" label="成交量" width="120" align="right">
          <template #default="{ row }">
            {{ formatVolume(row.volume) }}
          </template>
        </el-table-column>

        <el-table-column prop="amount" label="成交额" width="120" align="right">
          <template #default="{ row }">
            {{ formatAmount(row.amount) }}
          </template>
        </el-table-column>

        <el-table-column prop="market" label="市场" width="80" align="center">
          <template #default="{ row }">
            <el-tag :type="row.market === 'SH' ? 'danger' : 'primary'" size="small">
              {{ row.market }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="sector" label="行业" width="100">
          <template #default="{ row }">
            <span class="sector-tag">{{ row.sector }}</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-button-group size="small">
              <el-button @click.stop="viewDetail(row)" size="small">
                <el-icon><View /></el-icon>
              </el-button>
              <el-button @click.stop="addToWatchlist(row)" size="small">
                <el-icon><Star /></el-icon>
              </el-button>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[20, 50, 100, 200]"
          :total="totalStocks"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'
import {
  TrendCharts, Money, ArrowUp, ArrowDown, Connection, Search, Refresh,
  VideoPlay, VideoPause, View, Star
} from '@element-plus/icons-vue'
import { marketApi } from '@/api/market_unified'

// 响应式数据
const router = useRouter()
const loading = ref(false)
const connecting = ref(false)
const isConnected = ref(false)
const lastUpdateTime = ref('')
const connectionStatus = ref('未连接')

// 搜索和筛选
const searchQuery = ref('')
const selectedMarket = ref('')
const selectedSector = ref('')
const sortBy = ref('change_percent')

// 分页
const currentPage = ref(1)
const pageSize = ref(50)
const totalStocks = ref(0)

// 数据
const stocks = ref([])
const sectors = ref(['银行', '房地产', '食品饮料', '家电', '电子', '新能源', '医药', '保险'])
const realtimeStats = reactive({
  activeStocks: 0,
  totalVolume: 0,
  totalAmount: 0,
  upCount: 0,
  downCount: 0
})

// WebSocket连接
let websocket: WebSocket | null = null
let reconnectTimer: NodeJS.Timeout | null = null
let updateTimer: NodeJS.Timeout | null = null

// 计算属性
const filteredStocks = computed(() => {
  let result = stocks.value

  // 搜索过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter(stock =>
      stock.code.toLowerCase().includes(query) ||
      stock.name.toLowerCase().includes(query)
    )
  }

  // 市场过滤
  if (selectedMarket.value) {
    result = result.filter(stock => stock.market === selectedMarket.value)
  }

  // 行业过滤
  if (selectedSector.value) {
    result = result.filter(stock => stock.sector === selectedSector.value)
  }

  // 排序
  if (sortBy.value) {
    result.sort((a, b) => {
      const aVal = a[sortBy.value] || 0
      const bVal = b[sortBy.value] || 0
      return sortBy.value === 'code' ? aVal.localeCompare(bVal) : bVal - aVal
    })
  }

  return result
})

// 生命周期
onMounted(() => {
  loadInitialData()
  startUpdateTimer()
})

onUnmounted(() => {
  disconnectWebSocket()
  if (updateTimer) {
    clearInterval(updateTimer)
  }
})

// 方法
const loadInitialData = async () => {
  loading.value = true
  try {
    const response = await marketApi.getStocks({
      page: currentPage.value,
      pageSize: pageSize.value
    })

    if (response.success) {
      stocks.value = response.data || []
      totalStocks.value = response.pagination?.total || 0
      updateRealtimeStats()
    }
  } catch (error) {
    console.error('加载股票数据失败:', error)
    ElMessage.error('加载股票数据失败')
  } finally {
    loading.value = false
  }
}

const updateRealtimeStats = () => {
  const stats = stocks.value.reduce((acc, stock) => {
    acc.activeStocks++
    acc.totalVolume += stock.volume || 0
    acc.totalAmount += stock.amount || 0
    if ((stock.change_percent || 0) > 0) acc.upCount++
    if ((stock.change_percent || 0) < 0) acc.downCount++
    return acc
  }, { activeStocks: 0, totalVolume: 0, totalAmount: 0, upCount: 0, downCount: 0 })

  Object.assign(realtimeStats, stats)
}

const toggleConnection = () => {
  if (isConnected.value) {
    disconnectWebSocket()
  } else {
    connectWebSocket()
  }
}

const connectWebSocket = () => {
  connecting.value = true
  // 模拟WebSocket连接
  setTimeout(() => {
    isConnected.value = true
    connecting.value = false
    connectionStatus.value = '已连接'
    ElMessage.success('实时连接已建立')
  }, 1000)
}

const disconnectWebSocket = () => {
  isConnected.value = false
  connectionStatus.value = '已断开'
  if (websocket) {
    websocket.close()
    websocket = null
  }
}

const startUpdateTimer = () => {
  updateTimer = setInterval(() => {
    lastUpdateTime.value = getCurrentTime()
    if (isConnected.value) {
      // 模拟实时数据更新
      simulateRealtimeUpdate()
    }
  }, 1000)
}

const simulateRealtimeUpdate = () => {
  // 模拟股票价格变化
  stocks.value.forEach(stock => {
    const change = (Math.random() - 0.5) * 0.02 // ±1%的随机变化
    stock.change_percent = (stock.change_percent || 0) + change
    stock.current_price = stock.current_price * (1 + change / 100)
  })
  updateRealtimeStats()
}

// 事件处理
const handleSearch = () => {
  // 搜索逻辑已在计算属性中处理
}

const handleMarketChange = () => {
  // 市场筛选逻辑已在计算属性中处理
}

const handleSectorChange = () => {
  // 行业筛选逻辑已在计算属性中处理
}

const handleSort = () => {
  // 排序逻辑已在计算属性中处理
}

const refreshData = () => {
  loadInitialData()
}

const handleRowClick = (row: any) => {
  router.push(`/market/${row.code}`)
}

const viewDetail = (row: any) => {
  router.push(`/market/${row.code}`)
}

const addToWatchlist = (row: any) => {
  ElMessage.success(`已添加 ${row.name} 到自选股`)
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  loadInitialData()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  loadInitialData()
}

// 工具函数
const getCurrentTime = () => {
  return new Date().toLocaleTimeString()
}

const getPriceClass = (changePercent: number) => {
  if (changePercent > 0) return 'price-up'
  if (changePercent < 0) return 'price-down'
  return 'price-neutral'
}

const formatPrice = (price: number) => {
  return price?.toFixed(2) || '0.00'
}

const formatChange = (change: number) => {
  const formatted = Math.abs(change || 0).toFixed(2)
  return change > 0 ? `+${formatted}` : change < 0 ? `-${formatted}` : '0.00'
}

const formatPercent = (percent: number) => {
  const formatted = Math.abs(percent || 0).toFixed(2)
  return percent > 0 ? `+${formatted}` : percent < 0 ? `-${formatted}` : '0.00'
}

const formatVolume = (volume: number) => {
  if (!volume) return '0'
  if (volume >= 100000000) return `${(volume / 100000000).toFixed(1)}亿`
  if (volume >= 10000) return `${(volume / 10000).toFixed(1)}万`
  return volume.toString()
}

const formatAmount = (amount: number) => {
  if (!amount) return '0'
  if (amount >= 100000000) return `${(amount / 100000000).toFixed(1)}亿`
  if (amount >= 10000) return `${(amount / 10000).toFixed(1)}万`
  return amount.toString()
}

const formatNumber = (num: number) => {
  return num?.toLocaleString() || '0'
}
</script>

<style scoped>
.realtime-market {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px;
}

.page-header h1 {
  margin: 0 0 10px 0;
  font-size: 28px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  opacity: 0.9;
  font-size: 16px;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  border: none;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.stat-card.realtime {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
}

.stat-content {
  position: relative;
  z-index: 2;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  opacity: 0.8;
  margin-bottom: 5px;
}

.stat-status {
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.stat-status.online {
  color: #67c23a;
}

.stat-detail {
  font-size: 12px;
  opacity: 0.7;
}

.stat-icon {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 32px;
  opacity: 0.3;
}

.stat-icon.up {
  color: #f56c6c;
}

.stat-icon.online {
  color: #67c23a;
}

.search-card, .table-card {
  border: none;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 15px;
}

.update-time {
  font-size: 12px;
  color: #909399;
}

.realtime-table {
  font-size: 14px;
}

.stock-code {
  font-family: 'Courier New', monospace;
  font-weight: bold;
  color: #409eff;
}

.stock-name {
  font-weight: 500;
}

.price-up {
  color: #f56c6c;
  font-weight: bold;
}

.price-down {
  color: #67c23a;
  font-weight: bold;
}

.price-neutral {
  color: #909399;
}

.sector-tag {
  font-size: 12px;
  color: #606266;
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: center;
}

/* 动画效果 */
@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.7; }
  100% { opacity: 1; }
}

.stat-card.realtime .stat-status.online {
  animation: pulse 2s infinite;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .realtime-market {
    padding: 10px;
  }

  .stats-cards .el-col {
    margin-bottom: 10px;
  }

  .page-header h1 {
    font-size: 24px;
  }

  .stat-number {
    font-size: 20px;
  }
}
</style>
