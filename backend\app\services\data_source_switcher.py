"""
数据源切换服务
统一管理Mock数据和真实数据的切换逻辑
解决数据源配置混乱和切换不一致的问题
"""

import logging
from typing import Any, Dict, List, Optional, Tuple
from enum import Enum
from datetime import datetime

from app.core.config import settings

logger = logging.getLogger(__name__)


class DataSourceType(Enum):
    """数据源类型"""
    MOCK = "mock"
    TUSHARE = "tushare"
    AKSHARE = "akshare"
    CTP = "ctp"


class DataSourceStatus(Enum):
    """数据源状态"""
    AVAILABLE = "available"
    UNAVAILABLE = "unavailable"
    ERROR = "error"
    TESTING = "testing"


class DataSourceSwitcher:
    """数据源切换管理器"""
    
    def __init__(self):
        self.source_status: Dict[DataSourceType, DataSourceStatus] = {}
        self.fallback_chain: List[DataSourceType] = []
        self.current_source: Optional[DataSourceType] = None
        self._initialize()
    
    def _initialize(self):
        """初始化数据源配置"""
        # 根据配置建立优先级链
        priority_list = settings.data_source_priority
        
        # 转换为枚举类型
        for source_name in priority_list:
            try:
                source_type = DataSourceType(source_name.lower())
                self.fallback_chain.append(source_type)
                self.source_status[source_type] = DataSourceStatus.TESTING
            except ValueError:
                logger.warning(f"Unknown data source in config: {source_name}")
        
        # 如果没有配置，使用默认优先级
        if not self.fallback_chain:
            self.fallback_chain = [DataSourceType.MOCK]
            self.source_status[DataSourceType.MOCK] = DataSourceStatus.AVAILABLE
        
        logger.info(f"Data source fallback chain: {[s.value for s in self.fallback_chain]}")
        self._test_all_sources()
    
    def _test_all_sources(self):
        """测试所有数据源的可用性"""
        for source_type in self.fallback_chain:
            try:
                if self._test_source(source_type):
                    self.source_status[source_type] = DataSourceStatus.AVAILABLE
                    if self.current_source is None:
                        self.current_source = source_type
                        logger.info(f"Current data source set to: {source_type.value}")
                else:
                    self.source_status[source_type] = DataSourceStatus.UNAVAILABLE
            except Exception as e:
                logger.error(f"Error testing source {source_type.value}: {e}")
                self.source_status[source_type] = DataSourceStatus.ERROR
        
        # 确保至少有Mock数据可用
        if not self.current_source:
            self.current_source = DataSourceType.MOCK
            self.source_status[DataSourceType.MOCK] = DataSourceStatus.AVAILABLE
            logger.warning("No data sources available, falling back to MOCK")
    
    def _test_source(self, source_type: DataSourceType) -> bool:
        """测试特定数据源是否可用"""
        if source_type == DataSourceType.MOCK:
            return True  # Mock数据总是可用
        
        elif source_type == DataSourceType.TUSHARE:
            return bool(settings.TUSHARE_API_TOKEN)
        
        elif source_type == DataSourceType.AKSHARE:
            return settings.AKSHARE_ENABLED
        
        elif source_type == DataSourceType.CTP:
            # 检查CTP相关配置
            return hasattr(settings, 'CTP_ENABLED') and getattr(settings, 'CTP_ENABLED', False)
        
        return False
    
    def get_current_source(self) -> DataSourceType:
        """获取当前数据源"""
        if not self.current_source:
            self.current_source = DataSourceType.MOCK
        return self.current_source
    
    def switch_to_source(self, source_type: DataSourceType) -> bool:
        """手动切换到指定数据源"""
        if source_type not in self.source_status:
            logger.error(f"Unknown data source: {source_type}")
            return False
        
        # 测试数据源可用性
        if self._test_source(source_type):
            self.current_source = source_type
            self.source_status[source_type] = DataSourceStatus.AVAILABLE
            logger.info(f"Switched to data source: {source_type.value}")
            return True
        else:
            self.source_status[source_type] = DataSourceStatus.UNAVAILABLE
            logger.error(f"Cannot switch to unavailable source: {source_type.value}")
            return False
    
    def failover_to_next(self) -> Optional[DataSourceType]:
        """故障转移到下一个可用数据源"""
        current_index = -1
        if self.current_source:
            try:
                current_index = self.fallback_chain.index(self.current_source)
            except ValueError:
                pass
        
        # 尝试后续的数据源
        for i in range(current_index + 1, len(self.fallback_chain)):
            candidate = self.fallback_chain[i]
            if self._test_source(candidate):
                old_source = self.current_source
                self.current_source = candidate
                self.source_status[candidate] = DataSourceStatus.AVAILABLE
                
                if old_source:
                    self.source_status[old_source] = DataSourceStatus.ERROR
                
                logger.info(f"Failover: {old_source.value if old_source else 'None'} -> {candidate.value}")
                return candidate
        
        # 如果没有找到可用源，回退到Mock
        if self.current_source != DataSourceType.MOCK:
            self.current_source = DataSourceType.MOCK
            self.source_status[DataSourceType.MOCK] = DataSourceStatus.AVAILABLE
            logger.warning("All sources failed, falling back to MOCK")
        
        return self.current_source
    
    def is_mock_mode(self) -> bool:
        """判断是否处于Mock模式"""
        return self.get_current_source() == DataSourceType.MOCK
    
    def is_real_data_mode(self) -> bool:
        """判断是否处于真实数据模式"""
        return not self.is_mock_mode()
    
    def get_source_config(self) -> Dict[str, Any]:
        """获取当前数据源配置信息"""
        current = self.get_current_source()
        
        config = {
            "current_source": current.value,
            "is_mock": self.is_mock_mode(),
            "use_real_data": settings.USE_REAL_DATA,
            "preferred_source": settings.PREFERRED_DATA_SOURCE,
            "fallback_chain": [s.value for s in self.fallback_chain],
            "source_status": {k.value: v.value for k, v in self.source_status.items()}
        }
        
        # 添加特定源的配置
        if current == DataSourceType.TUSHARE:
            config["tushare_token_configured"] = bool(settings.TUSHARE_API_TOKEN)
        elif current == DataSourceType.AKSHARE:
            config["akshare_enabled"] = settings.AKSHARE_ENABLED
        
        return config
    
    def get_health_status(self) -> Dict[str, Any]:
        """获取数据源健康状态"""
        total_sources = len(self.source_status)
        available_sources = sum(1 for status in self.source_status.values() 
                               if status == DataSourceStatus.AVAILABLE)
        
        health_score = (available_sources / total_sources) * 100 if total_sources > 0 else 0
        
        return {
            "overall_health": "healthy" if health_score >= 50 else "degraded" if health_score > 0 else "critical",
            "health_score": round(health_score, 2),
            "total_sources": total_sources,
            "available_sources": available_sources,
            "current_source": self.get_current_source().value,
            "last_check": datetime.now().isoformat(),
            "details": self.get_source_config()
        }
    
    async def refresh_source_status(self):
        """刷新所有数据源状态"""
        logger.info("Refreshing data source status...")
        old_current = self.current_source
        
        self._test_all_sources()
        
        # 如果当前源变了，记录日志
        if old_current != self.current_source:
            logger.info(f"Data source changed during refresh: {old_current.value if old_current else 'None'} -> {self.current_source.value}")
    
    def simulate_source_failure(self, source_type: DataSourceType):
        """模拟数据源故障 (用于测试)"""
        if source_type in self.source_status:
            self.source_status[source_type] = DataSourceStatus.ERROR
            
            if self.current_source == source_type:
                logger.warning(f"Simulating failure of current source: {source_type.value}")
                self.failover_to_next()
    
    def get_mock_data_config(self) -> Dict[str, Any]:
        """获取Mock数据配置"""
        return {
            "enable_realistic_prices": True,
            "enable_random_fluctuation": True,
            "price_range": {"min": 1.0, "max": 1000.0},
            "volume_range": {"min": 100000, "max": 50000000},
            "update_interval_seconds": 3,
            "supported_symbols": ["000001", "000002", "600000", "600036"],
            "kline_intervals": ["1m", "5m", "15m", "30m", "1h", "1d", "1w"]
        }


# 全局数据源切换器实例
data_source_switcher = DataSourceSwitcher()


# 便捷函数
def get_current_data_source() -> DataSourceType:
    """获取当前数据源"""
    return data_source_switcher.get_current_source()


def is_mock_mode() -> bool:
    """是否为Mock模式"""
    return data_source_switcher.is_mock_mode()


def is_real_data_mode() -> bool:
    """是否为真实数据模式"""  
    return data_source_switcher.is_real_data_mode()


def switch_to_mock():
    """切换到Mock模式"""
    return data_source_switcher.switch_to_source(DataSourceType.MOCK)


def switch_to_real_data():
    """切换到真实数据模式"""
    # 尝试切换到第一个非Mock数据源
    for source_type in data_source_switcher.fallback_chain:
        if source_type != DataSourceType.MOCK:
            if data_source_switcher.switch_to_source(source_type):
                return True
    return False


async def handle_source_error(source_type: DataSourceType, error: Exception):
    """处理数据源错误"""
    logger.error(f"Data source error for {source_type.value}: {error}")
    
    # 如果是当前源发生错误，进行故障转移
    if data_source_switcher.current_source == source_type:
        next_source = data_source_switcher.failover_to_next()
        logger.info(f"Failover completed, now using: {next_source.value if next_source else 'None'}")


def get_source_display_info() -> Dict[str, Any]:
    """获取用于前端显示的数据源信息"""
    current = data_source_switcher.get_current_source()
    
    display_names = {
        DataSourceType.MOCK: "模拟数据",
        DataSourceType.TUSHARE: "Tushare财经数据",
        DataSourceType.AKSHARE: "AkShare开源数据", 
        DataSourceType.CTP: "CTP实时数据"
    }
    
    descriptions = {
        DataSourceType.MOCK: "用于开发和测试的模拟行情数据",
        DataSourceType.TUSHARE: "专业金融数据服务，需要Token",
        DataSourceType.AKSHARE: "开源免费金融数据接口",
        DataSourceType.CTP: "期货公司CTP接口实时数据"
    }
    
    return {
        "current_source": {
            "type": current.value,
            "name": display_names.get(current, "未知数据源"),
            "description": descriptions.get(current, ""),
            "is_mock": current == DataSourceType.MOCK
        },
        "available_sources": [
            {
                "type": source.value,
                "name": display_names.get(source, source.value),
                "description": descriptions.get(source, ""),
                "status": data_source_switcher.source_status.get(source, DataSourceStatus.UNAVAILABLE).value,
                "is_current": source == current
            }
            for source in data_source_switcher.fallback_chain
        ],
        "health": data_source_switcher.get_health_status()
    }