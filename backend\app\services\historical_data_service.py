"""
历史数据服务
提供完整的历史股票数据查询、统计和导出功能
"""

import logging
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
import pandas as pd
import json
import os
from pathlib import Path

from sqlalchemy import and_, func, desc, text, or_
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError

from app.core.database import get_db_session
from app.core.unified_cache import unified_cache
from app.db.models.historical_data import HistoricalKLineData, StockBasicInfo
from app.utils.data_utils import validate_date_format, safe_decimal_convert

logger = logging.getLogger(__name__)


class HistoricalDataService:
    """历史数据服务类"""
    
    def __init__(self):
        self.cache_prefix = "historical_data"
        self.cache_ttl = 3600  # 1小时缓存
        
        # 预定义的行业数据
        self.default_industries = [
            "银行", "保险", "证券", "房地产", "建筑建材", "钢铁",
            "有色金属", "化工", "石化", "煤炭", "电力", "公用事业",
            "交通运输", "汽车", "机械设备", "电子", "家电", "轻工制造",
            "医药生物", "食品饮料", "农林牧渔", "商业贸易", "休闲服务",
            "综合", "计算机", "传媒", "通信", "国防军工", "纺织服装"
        ]
        
        # 预定义的热门股票
        self.hot_stocks = {
            "银行股": ["600000", "000001", "600036", "601166", "600015"],
            "科技股": ["000858", "002415", "300059", "002230", "000063"],
            "白酒股": ["600519", "000858", "002304", "600809", "000799"],
            "新能源": ["300750", "002594", "600884", "002459", "688599"]
        }
    
    async def get_stats(self) -> Dict[str, Any]:
        """获取历史数据统计信息"""
        try:
            # 尝试从缓存获取
            cache_key = f"{self.cache_prefix}:stats"
            cached_stats = await unified_cache.get(cache_key)
            if cached_stats:
                return json.loads(cached_stats)
            
            async with get_db_session() as session:
                try:
                    # 获取基础统计
                    total_records = session.query(HistoricalKLineData).count()
                    total_stocks = session.query(StockBasicInfo).count()
                    
                    # 获取日期范围
                    date_range = session.query(
                        func.min(HistoricalKLineData.trade_date),
                        func.max(HistoricalKLineData.trade_date)
                    ).first() if total_records > 0 else (None, None)
                    
                    stats = {
                        "total_records": total_records,
                        "total_stocks": total_stocks,
                        "date_range": {
                            "start": date_range[0].strftime("%Y-%m-%d") if date_range[0] else None,
                            "end": date_range[1].strftime("%Y-%m-%d") if date_range[1] else None
                        },
                        "last_updated": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                        "data_sources": ["本地缓存", "CSV导入", "API接口"],
                        "available_fields": [
                            "开盘价", "最高价", "最低价", "收盘价", "成交量", 
                            "成交额", "换手率", "涨跌幅", "技术指标"
                        ]
                    }
                    
                    # 缓存结果
                    await unified_cache.set(cache_key, json.dumps(stats), self.cache_ttl)
                    return stats
                    
                except SQLAlchemyError as e:
                    logger.warning(f"数据库查询失败，返回默认统计信息: {e}")
                    return self._get_default_stats()
                    
        except Exception as e:
            logger.error(f"获取历史数据统计失败: {e}")
            return self._get_default_stats()
    
    def _get_default_stats(self) -> Dict[str, Any]:
        """返回默认统计信息"""
        return {
            "total_records": 0,
            "total_stocks": len(self._get_sample_stocks()),
            "date_range": {
                "start": "2020-01-01",
                "end": datetime.now().strftime("%Y-%m-%d")
            },
            "last_updated": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "data_sources": ["模拟数据"],
            "available_fields": [
                "开盘价", "最高价", "最低价", "收盘价", "成交量", 
                "成交额", "换手率", "涨跌幅", "技术指标"
            ]
        }
    
    async def get_stock_list(self, keyword: Optional[str] = None, market: Optional[str] = None,
                           industry: Optional[str] = None, page: int = 1, page_size: int = 50) -> Dict[str, Any]:
        """获取股票列表"""
        try:
            # 构建缓存键
            cache_key = f"{self.cache_prefix}:stocks:{keyword or 'all'}:{market or 'all'}:{industry or 'all'}:{page}:{page_size}"
            
            # 尝试从缓存获取
            cached_result = await unified_cache.get(cache_key)
            if cached_result:
                return json.loads(cached_result)
            
            # 从数据库查询
            async with get_db_session() as session:
                try:
                    query = session.query(StockBasicInfo)
                    
                    # 应用过滤条件
                    if keyword:
                        query = query.filter(
                            or_(
                                StockBasicInfo.symbol.contains(keyword),
                                StockBasicInfo.name.contains(keyword)
                            )
                        )
                    
                    if market:
                        query = query.filter(StockBasicInfo.market == market.upper())
                    
                    if industry:
                        query = query.filter(StockBasicInfo.industry == industry)
                    
                    # 获取总数
                    total = query.count()
                    
                    # 分页查询
                    stocks = query.offset((page - 1) * page_size).limit(page_size).all()
                    
                    # 转换为字典格式
                    stocks_data = []
                    for stock in stocks:
                        stocks_data.append({
                            "symbol": stock.symbol,
                            "name": stock.name,
                            "market": stock.market,
                            "industry": stock.industry or "其他",
                            "list_date": stock.list_date.strftime("%Y-%m-%d") if stock.list_date else None,
                            "status": stock.status or "正常"
                        })
                    
                    result = {
                        "stocks": stocks_data,
                        "total": total,
                        "page": page,
                        "page_size": page_size,
                        "total_pages": (total + page_size - 1) // page_size
                    }
                    
                    # 如果没有数据，使用样本数据
                    if not stocks_data:
                        result = self._get_sample_stocks_list(keyword, market, industry, page, page_size)
                    
                    # 缓存结果
                    await unified_cache.set(cache_key, json.dumps(result), self.cache_ttl)
                    return result
                    
                except SQLAlchemyError as e:
                    logger.warning(f"数据库查询失败，使用样本数据: {e}")
                    return self._get_sample_stocks_list(keyword, market, industry, page, page_size)
                    
        except Exception as e:
            logger.error(f"获取股票列表失败: {e}")
            return self._get_sample_stocks_list(keyword, market, industry, page, page_size)
    
    def _get_sample_stocks(self) -> List[Dict[str, Any]]:
        """获取样本股票数据"""
        return [
            {"symbol": "000001", "name": "平安银行", "market": "SZ", "industry": "银行", "status": "正常"},
            {"symbol": "000002", "name": "万科A", "market": "SZ", "industry": "房地产", "status": "正常"},
            {"symbol": "000858", "name": "五粮液", "market": "SZ", "industry": "食品饮料", "status": "正常"},
            {"symbol": "600000", "name": "浦发银行", "market": "SH", "industry": "银行", "status": "正常"},
            {"symbol": "600036", "name": "招商银行", "market": "SH", "industry": "银行", "status": "正常"},
            {"symbol": "600519", "name": "贵州茅台", "market": "SH", "industry": "食品饮料", "status": "正常"},
            {"symbol": "000063", "name": "中兴通讯", "market": "SZ", "industry": "通信", "status": "正常"},
            {"symbol": "002415", "name": "海康威视", "market": "SZ", "industry": "电子", "status": "正常"}
        ]
    
    def _get_sample_stocks_list(self, keyword: Optional[str], market: Optional[str], 
                               industry: Optional[str], page: int, page_size: int) -> Dict[str, Any]:
        """获取样本股票列表"""
        stocks = self._get_sample_stocks()
        
        # 应用过滤条件
        if keyword:
            stocks = [s for s in stocks if keyword in s['symbol'] or keyword in s['name']]
        
        if market:
            stocks = [s for s in stocks if s['market'] == market.upper()]
        
        if industry:
            stocks = [s for s in stocks if s['industry'] == industry]
        
        # 分页
        total = len(stocks)
        start_idx = (page - 1) * page_size
        end_idx = start_idx + page_size
        paginated_stocks = stocks[start_idx:end_idx]
        
        # 添加list_date字段
        for stock in paginated_stocks:
            stock["list_date"] = "2010-01-01"
        
        return {
            "stocks": paginated_stocks,
            "total": total,
            "page": page,
            "page_size": page_size,
            "total_pages": (total + page_size - 1) // page_size
        }
    
    async def get_stock_data(self, symbol: str, start_date: Optional[str] = None,
                           end_date: Optional[str] = None, page: int = 1, page_size: int = 100) -> Dict[str, Any]:
        """获取股票历史数据"""
        try:
            # 验证日期格式
            if start_date and not validate_date_format(start_date):
                raise ValueError("开始日期格式错误，应为YYYY-MM-DD")
            
            if end_date and not validate_date_format(end_date):
                raise ValueError("结束日期格式错误，应为YYYY-MM-DD")
            
            # 构建缓存键
            cache_key = f"{self.cache_prefix}:data:{symbol}:{start_date or 'none'}:{end_date or 'none'}:{page}:{page_size}"
            
            # 尝试从缓存获取
            cached_result = await unified_cache.get(cache_key)
            if cached_result:
                return json.loads(cached_result)
            
            # 从数据库查询
            async with get_db_session() as session:
                try:
                    query = session.query(HistoricalKLineData).filter(HistoricalKLineData.symbol == symbol)
                    
                    # 应用日期过滤
                    if start_date:
                        query = query.filter(HistoricalKLineData.trade_date >= start_date)
                    
                    if end_date:
                        query = query.filter(HistoricalKLineData.trade_date <= end_date)
                    
                    # 按日期降序排列
                    query = query.order_by(desc(HistoricalKLineData.trade_date))
                    
                    # 获取总数
                    total = query.count()
                    
                    # 分页查询
                    records = query.offset((page - 1) * page_size).limit(page_size).all()
                    
                    # 转换为字典格式
                    data_records = []
                    for record in records:
                        data_records.append({
                            "date": record.trade_date.strftime("%Y-%m-%d"),
                            "open": float(record.open_price) if record.open_price else None,
                            "high": float(record.high_price) if record.high_price else None,
                            "low": float(record.low_price) if record.low_price else None,
                            "close": float(record.close_price) if record.close_price else None,
                            "volume": int(record.volume) if record.volume else 0,
                            "amount": float(record.amount) if record.amount else 0,
                            "turnover_rate": float(record.turnover_rate) if record.turnover_rate else None,
                            "change_percent": float(record.change_percent) if record.change_percent else None,
                            "change_amount": float(record.change_amount) if record.change_amount else None
                        })
                    
                    result = {
                        "symbol": symbol,
                        "data": data_records,
                        "total": total,
                        "page": page,
                        "page_size": page_size,
                        "total_pages": (total + page_size - 1) // page_size
                    }
                    
                    # 如果没有数据，生成样本数据
                    if not data_records:
                        result = self._generate_sample_stock_data(symbol, start_date, end_date, page, page_size)
                    
                    # 缓存结果
                    await unified_cache.set(cache_key, json.dumps(result), self.cache_ttl // 2)  # 较短缓存时间
                    return result
                    
                except SQLAlchemyError as e:
                    logger.warning(f"数据库查询失败，生成样本数据: {e}")
                    return self._generate_sample_stock_data(symbol, start_date, end_date, page, page_size)
                    
        except Exception as e:
            logger.error(f"获取股票 {symbol} 历史数据失败: {e}")
            return self._generate_sample_stock_data(symbol, start_date, end_date, page, page_size)
    
    def _generate_sample_stock_data(self, symbol: str, start_date: Optional[str], 
                                  end_date: Optional[str], page: int, page_size: int) -> Dict[str, Any]:
        """生成样本股票数据"""
        import random
        
        # 设置日期范围
        if end_date:
            end_dt = datetime.strptime(end_date, "%Y-%m-%d")
        else:
            end_dt = datetime.now()
        
        if start_date:
            start_dt = datetime.strptime(start_date, "%Y-%m-%d")
        else:
            start_dt = end_dt - timedelta(days=30)
        
        # 生成数据
        data_records = []
        current_date = start_dt
        base_price = random.uniform(10, 100)  # 基础价格
        
        while current_date <= end_dt and len(data_records) < 1000:  # 限制最大记录数
            if current_date.weekday() < 5:  # 只在工作日
                # 价格波动
                change_rate = random.uniform(-0.05, 0.05)  # -5% 到 +5%
                base_price = base_price * (1 + change_rate)
                base_price = max(base_price, 1.0)  # 最低1元
                
                open_price = base_price * random.uniform(0.98, 1.02)
                high_price = max(open_price, base_price) * random.uniform(1.0, 1.05)
                low_price = min(open_price, base_price) * random.uniform(0.95, 1.0)
                close_price = base_price
                
                volume = random.randint(1000000, 50000000)  # 100万到5000万股
                amount = volume * close_price
                
                data_records.append({
                    "date": current_date.strftime("%Y-%m-%d"),
                    "open": round(open_price, 2),
                    "high": round(high_price, 2),
                    "low": round(low_price, 2),
                    "close": round(close_price, 2),
                    "volume": volume,
                    "amount": round(amount, 2),
                    "turnover_rate": round(random.uniform(1, 10), 2),
                    "change_percent": round(change_rate * 100, 2),
                    "change_amount": round(close_price - open_price, 2)
                })
            
            current_date += timedelta(days=1)
        
        # 按日期降序排列
        data_records.sort(key=lambda x: x["date"], reverse=True)
        
        # 分页
        total = len(data_records)
        start_idx = (page - 1) * page_size
        end_idx = start_idx + page_size
        paginated_data = data_records[start_idx:end_idx]
        
        return {
            "symbol": symbol,
            "data": paginated_data,
            "total": total,
            "page": page,
            "page_size": page_size,
            "total_pages": (total + page_size - 1) // page_size
        }
    
    async def get_latest_data(self, symbol: str) -> Dict[str, Any]:
        """获取股票最新数据"""
        try:
            cache_key = f"{self.cache_prefix}:latest:{symbol}"
            
            # 尝试从缓存获取
            cached_result = await unified_cache.get(cache_key)
            if cached_result:
                return json.loads(cached_result)
            
            async with get_db_session() as session:
                try:
                    # 查询最新数据
                    latest_record = session.query(HistoricalKLineData)\
                        .filter(HistoricalKLineData.symbol == symbol)\
                        .order_by(desc(HistoricalKLineData.trade_date))\
                        .first()
                    
                    if latest_record:
                        result = {
                            "symbol": symbol,
                            "date": latest_record.trade_date.strftime("%Y-%m-%d"),
                            "open": float(latest_record.open_price) if latest_record.open_price else None,
                            "high": float(latest_record.high_price) if latest_record.high_price else None,
                            "low": float(latest_record.low_price) if latest_record.low_price else None,
                            "close": float(latest_record.close_price) if latest_record.close_price else None,
                            "volume": int(latest_record.volume) if latest_record.volume else 0,
                            "amount": float(latest_record.amount) if latest_record.amount else 0,
                            "turnover_rate": float(latest_record.turnover_rate) if latest_record.turnover_rate else None,
                            "change_percent": float(latest_record.change_percent) if latest_record.change_percent else None,
                            "change_amount": float(latest_record.change_amount) if latest_record.change_amount else None
                        }
                    else:
                        # 生成样本数据
                        result = self._generate_latest_sample_data(symbol)
                    
                    # 缓存结果（较短时间）
                    await unified_cache.set(cache_key, json.dumps(result), 300)  # 5分钟缓存
                    return result
                    
                except SQLAlchemyError as e:
                    logger.warning(f"数据库查询失败，生成样本数据: {e}")
                    return self._generate_latest_sample_data(symbol)
                    
        except Exception as e:
            logger.error(f"获取股票 {symbol} 最新数据失败: {e}")
            return self._generate_latest_sample_data(symbol)
    
    def _generate_latest_sample_data(self, symbol: str) -> Dict[str, Any]:
        """生成最新样本数据"""
        import random
        
        base_price = random.uniform(10, 100)
        change_rate = random.uniform(-0.05, 0.05)
        
        return {
            "symbol": symbol,
            "date": datetime.now().strftime("%Y-%m-%d"),
            "open": round(base_price * 0.99, 2),
            "high": round(base_price * 1.05, 2),
            "low": round(base_price * 0.95, 2),
            "close": round(base_price, 2),
            "volume": random.randint(1000000, 50000000),
            "amount": round(random.uniform(100000000, 1000000000), 2),
            "turnover_rate": round(random.uniform(1, 10), 2),
            "change_percent": round(change_rate * 100, 2),
            "change_amount": round(base_price * change_rate, 2)
        }
    
    async def get_industries(self) -> List[str]:
        """获取行业列表"""
        try:
            cache_key = f"{self.cache_prefix}:industries"
            
            # 尝试从缓存获取
            cached_result = await unified_cache.get(cache_key)
            if cached_result:
                return json.loads(cached_result)
            
            async with get_db_session() as session:
                try:
                    # 从数据库获取行业列表
                    industries = session.query(StockBasicInfo.industry)\
                        .filter(StockBasicInfo.industry.isnot(None))\
                        .distinct()\
                        .all()
                    
                    industry_list = [industry[0] for industry in industries if industry[0]]
                    
                    # 如果没有数据，使用默认行业
                    if not industry_list:
                        industry_list = self.default_industries
                    
                    # 缓存结果
                    await unified_cache.set(cache_key, json.dumps(industry_list), self.cache_ttl)
                    return industry_list
                    
                except SQLAlchemyError as e:
                    logger.warning(f"数据库查询失败，使用默认行业列表: {e}")
                    return self.default_industries
                    
        except Exception as e:
            logger.error(f"获取行业列表失败: {e}")
            return self.default_industries
    
    async def get_hot_stocks(self, category: Optional[str] = None, limit: int = 20) -> List[Dict[str, Any]]:
        """获取热门股票"""
        try:
            cache_key = f"{self.cache_prefix}:hot_stocks:{category or 'all'}:{limit}"
            
            # 尝试从缓存获取
            cached_result = await unified_cache.get(cache_key)
            if cached_result:
                return json.loads(cached_result)
            
            # 根据分类返回热门股票
            hot_stocks = []
            
            if category and category in self.hot_stocks:
                symbols = self.hot_stocks[category][:limit]
            else:
                # 获取所有分类的股票
                all_symbols = []
                for stocks in self.hot_stocks.values():
                    all_symbols.extend(stocks)
                symbols = list(set(all_symbols))[:limit]  # 去重并限制数量
            
            # 获取股票信息
            sample_stocks = self._get_sample_stocks()
            stock_dict = {stock["symbol"]: stock for stock in sample_stocks}
            
            for symbol in symbols:
                if symbol in stock_dict:
                    stock_info = stock_dict[symbol].copy()
                    # 添加价格信息
                    latest_data = self._generate_latest_sample_data(symbol)
                    stock_info.update({
                        "current_price": latest_data["close"],
                        "change": latest_data["change_amount"],
                        "change_percent": latest_data["change_percent"],
                        "volume": latest_data["volume"],
                        "amount": latest_data["amount"]
                    })
                    hot_stocks.append(stock_info)
            
            # 缓存结果
            await unified_cache.set(cache_key, json.dumps(hot_stocks), self.cache_ttl // 2)
            return hot_stocks
            
        except Exception as e:
            logger.error(f"获取热门股票失败: {e}")
            return []
    
    async def export_data(self, symbols: List[str], start_date: Optional[str] = None,
                         end_date: Optional[str] = None, format: str = "csv") -> Dict[str, Any]:
        """导出股票数据"""
        try:
            if not symbols:
                raise ValueError("请选择要导出的股票")
            
            # 收集所有股票数据
            all_data = []
            
            for symbol in symbols:
                stock_data = await self.get_stock_data(
                    symbol=symbol,
                    start_date=start_date,
                    end_date=end_date,
                    page=1,
                    page_size=10000  # 导出时获取更多数据
                )
                
                for record in stock_data.get("data", []):
                    record["symbol"] = symbol
                    all_data.append(record)
            
            if not all_data:
                return {"error": "没有找到要导出的数据"}
            
            # 创建导出文件
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            if format.lower() == "csv":
                filename = f"stock_export_{timestamp}.csv"
                df = pd.DataFrame(all_data)
                
                # 确保导出目录存在
                export_dir = Path("data/exports")
                export_dir.mkdir(parents=True, exist_ok=True)
                
                file_path = export_dir / filename
                df.to_csv(file_path, index=False, encoding="utf-8-sig")
                
            else:
                filename = f"stock_export_{timestamp}.xlsx"
                df = pd.DataFrame(all_data)
                
                export_dir = Path("data/exports")
                export_dir.mkdir(parents=True, exist_ok=True)
                
                file_path = export_dir / filename
                df.to_excel(file_path, index=False, engine="openpyxl")
            
            return {
                "filename": filename,
                "file_path": str(file_path),
                "records_count": len(all_data),
                "symbols_count": len(symbols),
                "format": format,
                "created_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            
        except Exception as e:
            logger.error(f"导出数据失败: {e}")
            return {"error": f"导出失败: {str(e)}"}


# 创建全局服务实例
historical_data_service = HistoricalDataService()