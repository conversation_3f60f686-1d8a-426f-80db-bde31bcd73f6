<template>
  <div class="market-realtime-container">
    <!-- 市场指数 -->
    <div class="market-indices">
      <div
        v-for="index in indices"
        :key="index.code"
        class="index-item"
      >
        <div class="index-name">{{ index.name }}</div>
        <div class="index-value" :class="index.change >= 0 ? 'up' : 'down'">
          {{ index.value }}
        </div>
        <div class="index-change" :class="index.change >= 0 ? 'up' : 'down'">
          {{ index.change >= 0 ? '+' : '' }}{{ index.change }}
          ({{ index.changePercent >= 0 ? '+' : '' }}{{ index.changePercent.toFixed(2) }}%)
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 股票列表 -->
      <div class="stock-list">
        <div class="list-header">
          <el-input
            v-model="searchText"
            placeholder="搜索股票代码或名称"
            prefix-icon="Search"
            size="small"
            clearable
          />
          <div class="quick-filters">
            <el-button
              v-for="filter in quickFilters"
              :key="filter.value"
              :type="activeFilter === filter.value ? 'primary' : ''"
              size="small"
              @click="activeFilter = filter.value"
            >
              {{ filter.label }}
            </el-button>
          </div>
        </div>

        <div class="stock-items">
          <div
            v-for="stock in filteredStocks"
            :key="stock.code"
            class="stock-item"
            :class="{
              active: selectedStock?.code === stock.code,
              favorite: isFavorite(stock.code)
            }"
            @click="handleStockSelect(stock)"
          >
            <div class="stock-basic">
              <div class="stock-name">{{ stock.name }}</div>
              <div class="stock-code">{{ stock.code }}</div>
            </div>
            <div class="stock-price">
              <div class="price" :class="stock.changePercent >= 0 ? 'up' : 'down'">
                {{ stock.price }}
              </div>
              <div class="change" :class="stock.changePercent >= 0 ? 'up' : 'down'">
                {{ stock.changePercent >= 0 ? '+' : '' }}{{ stock.changePercent.toFixed(2) }}%
              </div>
            </div>
            <div class="stock-actions">
              <el-button
                v-if="!isFavorite(stock.code)"
                size="small"
                type="text"
                @click.stop="addToFavorites(stock)"
              >
                ⭐
              </el-button>
              <el-button
                v-else
                size="small"
                type="text"
                @click.stop="removeFromFavorites(stock.code)"
              >
                ★
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 图表区域 -->
      <div class="chart-area">
        <div class="chart-header">
          <div class="stock-info" v-if="selectedStock">
            <h2>{{ selectedStock.name }} ({{ selectedStock.code }})</h2>
            <div class="price-info">
              <span class="current-price" :class="selectedStock.changePercent >= 0 ? 'up' : 'down'">
                {{ selectedStock.price }}
              </span>
              <span class="price-change" :class="selectedStock.changePercent >= 0 ? 'up' : 'down'">
                {{ selectedStock.changePercent >= 0 ? '+' : '' }}{{ selectedStock.change }}
                ({{ selectedStock.changePercent >= 0 ? '+' : '' }}{{ selectedStock.changePercent.toFixed(2) }}%)
              </span>
            </div>
          </div>

          <div class="chart-controls">
            <el-button-group>
              <el-button
                :type="chartType === 'time' ? 'primary' : ''"
                size="small"
                @click="chartType = 'time'"
              >
                分时
              </el-button>
              <el-button
                :type="chartType === '1d' ? 'primary' : ''"
                size="small"
                @click="chartType = '1d'"
              >
                日K
              </el-button>
              <el-button
                :type="chartType === '1w' ? 'primary' : ''"
                size="small"
                @click="chartType = '1w'"
              >
                周K
              </el-button>
            </el-button-group>

            <el-select v-model="selectedIndicator" size="small" style="width: 100px; margin-left: 12px;">
              <el-option label="无指标" value="none" />
              <el-option label="MACD" value="macd" />
              <el-option label="KDJ" value="kdj" />
              <el-option label="RSI" value="rsi" />
            </el-select>
          </div>
        </div>

        <div class="kline-chart" ref="chartContainer"></div>

        <div class="key-metrics" v-if="selectedStock">
          <div class="metric-item">
            <span class="metric-label">今开</span>
            <span class="metric-value">{{ selectedStock.open }}</span>
          </div>
          <div class="metric-item">
            <span class="metric-label">昨收</span>
            <span class="metric-value">{{ selectedStock.preClose }}</span>
          </div>
          <div class="metric-item">
            <span class="metric-label">最高</span>
            <span class="metric-value up">{{ selectedStock.high }}</span>
          </div>
          <div class="metric-item">
            <span class="metric-label">最低</span>
            <span class="metric-value down">{{ selectedStock.low }}</span>
          </div>
          <div class="metric-item">
            <span class="metric-label">成交量</span>
            <span class="metric-value">{{ formatVolume(selectedStock.volume) }}</span>
          </div>
          <div class="metric-item">
            <span class="metric-label">成交额</span>
            <span class="metric-value">{{ formatAmount(selectedStock.amount) }}万</span>
          </div>
        </div>
      </div>

      <!-- 交易面板 -->
      <div class="trade-panel">
        <!-- 五档盘口 -->
        <div class="order-book">
          <h3>五档盘口</h3>

          <div class="order-list">
            <!-- 卖五档 -->
            <div class="sell-orders">
              <div
                v-for="(order, index) in mockOrderBook.ask"
                :key="`ask-${index}`"
                class="order-row"
              >
                <span class="order-level">卖{{ 5 - index }}</span>
                <span class="order-price up">{{ order.price }}</span>
                <span class="order-volume">{{ order.volume }}</span>
              </div>
            </div>

            <!-- 最新价 -->
            <div class="market-price" v-if="selectedStock">
              <span class="price" :class="selectedStock.changePercent >= 0 ? 'up' : 'down'">
                {{ selectedStock.price }}
              </span>
            </div>

            <!-- 买五档 -->
            <div class="buy-orders">
              <div
                v-for="(order, index) in mockOrderBook.bid"
                :key="`bid-${index}`"
                class="order-row"
              >
                <span class="order-level">买{{ index + 1 }}</span>
                <span class="order-price down">{{ order.price }}</span>
                <span class="order-volume">{{ order.volume }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 快速交易 -->
        <div class="quick-trade">
          <h4>快速下单</h4>

          <div class="trade-form">
            <el-radio-group v-model="tradeDirection" size="small">
              <el-radio-button label="buy">买入</el-radio-button>
              <el-radio-button label="sell">卖出</el-radio-button>
            </el-radio-group>

            <div class="price-input">
              <el-input
                v-model="tradePrice"
                placeholder="委托价格"
                size="small"
              />
              <el-button size="small" @click="setMarketPrice">市价</el-button>
            </div>

            <el-input
              v-model="tradeQuantity"
              placeholder="委托数量"
              size="small"
            >
              <template #append>手</template>
            </el-input>

            <div class="quick-quantity">
              <el-button
                v-for="qty in [100, 200, 500, 1000]"
                :key="qty"
                size="small"
                @click="tradeQuantity = qty"
              >
                {{ qty }}
              </el-button>
            </div>

            <el-button
              type="primary"
              :class="tradeDirection === 'buy' ? 'buy-button' : 'sell-button'"
              @click="submitTrade"
              size="small"
              block
            >
              {{ tradeDirection === 'buy' ? '买入' : '卖出' }}
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import * as echarts from 'echarts'

// 响应式数据
const searchText = ref('')
const activeFilter = ref('all')
const selectedStock = ref(null)
const chartType = ref('time')
const selectedIndicator = ref('none')
const tradeDirection = ref('buy')
const tradePrice = ref('')
const tradeQuantity = ref('')
const chartContainer = ref(null)
const chart = ref(null)

// 市场指数数据
const indices = ref([
  {
    code: '000001',
    name: '上证指数',
    value: 3245.68,
    change: 12.34,
    changePercent: 0.38
  },
  {
    code: '399001',
    name: '深证成指',
    value: 11725.45,
    change: -23.56,
    changePercent: -0.20
  },
  {
    code: '399006',
    name: '创业板指',
    value: 2405.67,
    change: 8.91,
    changePercent: 0.37
  }
])

// 快速筛选选项
const quickFilters = [
  { label: '全部', value: 'all' },
  { label: '自选', value: 'favorites' },
  { label: '涨幅榜', value: 'gainers' },
  { label: '跌幅榜', value: 'losers' },
  { label: '成交活跃', value: 'active' }
]

// 股票数据
const stocks = ref([
  {
    code: '000001',
    name: '平安银行',
    price: 12.45,
    change: -0.12,
    changePercent: -0.95,
    open: 12.60,
    preClose: 12.57,
    high: 12.68,
    low: 12.40,
    volume: 15570270,
    amount: 194256.8
  },
  {
    code: '000002',
    name: '万科A',
    price: 18.76,
    change: 0.34,
    changePercent: 1.85,
    open: 18.45,
    preClose: 18.42,
    high: 18.89,
    low: 18.32,
    volume: 8945632,
    amount: 167834.2
  },
  {
    code: '600036',
    name: '招商银行',
    price: 35.67,
    change: 0.89,
    changePercent: 2.56,
    open: 34.85,
    preClose: 34.78,
    high: 35.78,
    low: 34.76,
    volume: 12456789,
    amount: 442567.3
  },
  {
    code: '600519',
    name: '贵州茅台',
    price: 1678.50,
    change: -25.30,
    changePercent: -1.48,
    open: 1705.00,
    preClose: 1703.80,
    high: 1708.90,
    low: 1675.20,
    volume: 234567,
    amount: 394567.8
  }
])

// 收藏股票列表
const favoriteStocks = ref(['000001', '600036'])

// 模拟五档盘口数据
const mockOrderBook = reactive({
  ask: [
    { price: 12.48, volume: 1200 },
    { price: 12.47, volume: 800 },
    { price: 12.46, volume: 1500 },
    { price: 12.45, volume: 2000 },
    { price: 12.44, volume: 900 }
  ],
  bid: [
    { price: 12.43, volume: 1800 },
    { price: 12.42, volume: 1100 },
    { price: 12.41, volume: 2200 },
    { price: 12.40, volume: 1600 },
    { price: 12.39, volume: 1300 }
  ]
})

// 计算属性
const filteredStocks = computed(() => {
  let result = stocks.value

  // 搜索过滤
  if (searchText.value) {
    const query = searchText.value.toLowerCase()
    result = result.filter(stock =>
      stock.code.includes(query) ||
      stock.name.toLowerCase().includes(query)
    )
  }

  // 快速筛选
  switch (activeFilter.value) {
    case 'favorites':
      result = result.filter(stock => favoriteStocks.value.includes(stock.code))
      break
    case 'gainers':
      result = result.filter(stock => stock.changePercent > 0).sort((a, b) => b.changePercent - a.changePercent)
      break
    case 'losers':
      result = result.filter(stock => stock.changePercent < 0).sort((a, b) => a.changePercent - b.changePercent)
      break
    case 'active':
      result = result.sort((a, b) => b.volume - a.volume)
      break
  }

  return result
})

// 方法
const handleStockSelect = (stock) => {
  selectedStock.value = stock
  updateChart()
  updateOrderBook(stock.code)
}

const isFavorite = (code) => {
  return favoriteStocks.value.includes(code)
}

const addToFavorites = (stock) => {
  if (!favoriteStocks.value.includes(stock.code)) {
    favoriteStocks.value.push(stock.code)
    ElMessage.success(`已添加 ${stock.name} 到自选股`)
  }
}

const removeFromFavorites = (code) => {
  const index = favoriteStocks.value.indexOf(code)
  if (index > -1) {
    favoriteStocks.value.splice(index, 1)
    const stock = stocks.value.find(s => s.code === code)
    ElMessage.info(`已从自选股移除 ${stock?.name}`)
  }
}

const formatVolume = (volume) => {
  if (volume >= 100000000) {
    return (volume / 100000000).toFixed(2) + '亿'
  } else if (volume >= 10000) {
    return (volume / 10000).toFixed(2) + '万'
  }
  return volume.toString()
}

const formatAmount = (amount) => {
  if (amount >= 100000) {
    return (amount / 10000).toFixed(2) + '万'
  }
  return amount.toFixed(2)
}

const setMarketPrice = () => {
  if (selectedStock.value) {
    tradePrice.value = selectedStock.value.price
  }
}

const submitTrade = () => {
  if (!selectedStock.value) {
    ElMessage.warning('请先选择股票')
    return
  }

  if (!tradePrice.value || !tradeQuantity.value) {
    ElMessage.warning('请填写完整的交易信息')
    return
  }

  const action = tradeDirection.value === 'buy' ? '买入' : '卖出'
  ElMessage.success(`模拟${action}订单已提交: ${selectedStock.value.name} ${tradeQuantity.value}手 @${tradePrice.value}`)
}

// 图表相关方法
const initChart = () => {
  if (!chartContainer.value) return

  chart.value = echarts.init(chartContainer.value)
  updateChart()
}

const updateChart = () => {
  if (!chart.value || !selectedStock.value) return

  const option = {
    title: {
      text: `${selectedStock.value.name} (${selectedStock.value.code})`,
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      }
    },
    legend: {
      data: ['价格'],
      top: 30
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: generateTimeData()
    },
    yAxis: {
      type: 'value',
      scale: true,
      splitArea: {
        show: true
      }
    },
    series: [
      {
        name: '价格',
        type: 'line',
        data: generatePriceData(),
        smooth: true,
        lineStyle: {
          color: selectedStock.value.changePercent >= 0 ? '#f56c6c' : '#67c23a'
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [{
              offset: 0,
              color: selectedStock.value.changePercent >= 0 ? 'rgba(245, 108, 108, 0.3)' : 'rgba(103, 194, 58, 0.3)'
            }, {
              offset: 1,
              color: selectedStock.value.changePercent >= 0 ? 'rgba(245, 108, 108, 0.1)' : 'rgba(103, 194, 58, 0.1)'
            }]
          }
        }
      }
    ]
  }

  chart.value.setOption(option)
}

const generateTimeData = () => {
  const times = []
  const now = new Date()
  for (let i = 240; i >= 0; i--) {
    const time = new Date(now.getTime() - i * 60000)
    times.push(time.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' }))
  }
  return times
}

const generatePriceData = () => {
  if (!selectedStock.value) return []

  const basePrice = selectedStock.value.preClose
  const currentPrice = selectedStock.value.price
  const data = []

  for (let i = 0; i <= 240; i++) {
    const progress = i / 240
    const randomFactor = (Math.random() - 0.5) * 0.02
    const price = basePrice + (currentPrice - basePrice) * progress + basePrice * randomFactor
    data.push(price.toFixed(2))
  }

  return data
}

const updateOrderBook = async (symbol) => {
  // 模拟获取五档盘口数据
  try {
    // 这里应该调用真实的API
    // const response = await fetch(`/api/v1/market/quotes/${symbol}/orderbook`)
    // const data = await response.json()

    // 模拟数据更新
    const basePrice = selectedStock.value?.price || 12.45

    mockOrderBook.ask = Array.from({ length: 5 }, (_, i) => ({
      price: (basePrice + (i + 1) * 0.01).toFixed(2),
      volume: Math.floor(Math.random() * 2000) + 500
    }))

    mockOrderBook.bid = Array.from({ length: 5 }, (_, i) => ({
      price: (basePrice - (i + 1) * 0.01).toFixed(2),
      volume: Math.floor(Math.random() * 2000) + 500
    }))
  } catch (error) {
    console.error('获取盘口数据失败:', error)
  }
}

// 监听器
watch(chartType, () => {
  updateChart()
})

watch(selectedIndicator, () => {
  updateChart()
})

// 生命周期
onMounted(() => {
  initChart()
  // 默认选择第一只股票
  if (stocks.value.length > 0) {
    handleStockSelect(stocks.value[0])
  }

  // 模拟实时数据更新
  const interval = setInterval(() => {
    updateStockPrices()
  }, 3000)

  onUnmounted(() => {
    clearInterval(interval)
    if (chart.value) {
      chart.value.dispose()
    }
  })
})

const updateStockPrices = () => {
  stocks.value.forEach(stock => {
    const changeRate = (Math.random() - 0.5) * 0.02
    const newPrice = stock.price * (1 + changeRate)
    const change = newPrice - stock.preClose
    const changePercent = (change / stock.preClose) * 100

    stock.price = parseFloat(newPrice.toFixed(2))
    stock.change = parseFloat(change.toFixed(2))
    stock.changePercent = parseFloat(changePercent.toFixed(2))
  })

  // 更新选中股票的盘口数据
  if (selectedStock.value) {
    updateOrderBook(selectedStock.value.code)
  }
}
</script>

<style scoped>
.market-realtime-container {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

/* 市场指数 */
.market-indices {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.index-item {
  flex: 1;
  text-align: center;
  padding: 16px;
  border-radius: 6px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.index-name {
  font-size: 14px;
  margin-bottom: 8px;
  opacity: 0.9;
}

.index-value {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 4px;
}

.index-change {
  font-size: 12px;
  opacity: 0.8;
}

/* 主要内容区域 */
.main-content {
  display: grid;
  grid-template-columns: 300px 1fr 280px;
  gap: 20px;
  height: calc(100vh - 200px);
}

/* 股票列表 */
.stock-list {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.list-header {
  padding: 16px;
  border-bottom: 1px solid #ebeef5;
}

.quick-filters {
  display: flex;
  gap: 8px;
  margin-top: 12px;
}

.stock-items {
  height: calc(100% - 120px);
  overflow-y: auto;
}

.stock-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: all 0.2s;
}

.stock-item:hover {
  background: #f8f9fa;
}

.stock-item.active {
  background: #e6f7ff;
  border-left: 3px solid #1890ff;
}

.stock-item.favorite {
  background: #fff7e6;
}

.stock-basic {
  flex: 1;
}

.stock-name {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 4px;
}

.stock-code {
  font-size: 12px;
  color: #666;
}

.stock-price {
  text-align: right;
  margin-right: 8px;
}

.stock-price .price {
  font-size: 16px;
  font-weight: bold;
  display: block;
  margin-bottom: 2px;
}

.stock-price .change {
  font-size: 12px;
}

.stock-actions {
  width: 30px;
}

/* 图表区域 */
.chart-area {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #ebeef5;
}

.stock-info h2 {
  margin: 0 0 8px 0;
  font-size: 18px;
  color: #333;
}

.price-info {
  display: flex;
  gap: 12px;
  align-items: center;
}

.current-price {
  font-size: 24px;
  font-weight: bold;
}

.price-change {
  font-size: 14px;
}

.chart-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.kline-chart {
  flex: 1;
  min-height: 400px;
}

.key-metrics {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 16px;
  padding: 16px 20px;
  border-top: 1px solid #ebeef5;
  background: #fafafa;
}

.metric-item {
  text-align: center;
}

.metric-label {
  display: block;
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.metric-value {
  font-size: 14px;
  font-weight: 500;
}

/* 交易面板 */
.trade-panel {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.order-book {
  padding: 16px;
  border-bottom: 1px solid #ebeef5;
}

.order-book h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  color: #333;
}

.order-list {
  font-family: 'Courier New', monospace;
}

.order-row {
  display: grid;
  grid-template-columns: 40px 1fr 1fr;
  gap: 8px;
  padding: 4px 0;
  font-size: 12px;
  align-items: center;
}

.order-level {
  color: #666;
}

.order-price {
  text-align: right;
  font-weight: 500;
}

.order-volume {
  text-align: right;
  color: #666;
}

.market-price {
  text-align: center;
  padding: 8px 0;
  margin: 8px 0;
  background: #f0f0f0;
  border-radius: 4px;
}

.market-price .price {
  font-size: 16px;
  font-weight: bold;
}

.quick-trade {
  padding: 16px;
}

.quick-trade h4 {
  margin: 0 0 16px 0;
  font-size: 14px;
  color: #333;
}

.trade-form {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.price-input {
  display: flex;
  gap: 8px;
}

.price-input .el-input {
  flex: 1;
}

.quick-quantity {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 4px;
}

/* 颜色样式 */
.up {
  color: #f56c6c !important;
}

.down {
  color: #67c23a !important;
}

.buy-button {
  background: #f56c6c !important;
  border-color: #f56c6c !important;
}

.sell-button {
  background: #67c23a !important;
  border-color: #67c23a !important;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .main-content {
    grid-template-columns: 250px 1fr 250px;
  }
}

@media (max-width: 768px) {
  .main-content {
    grid-template-columns: 1fr;
    grid-template-rows: auto 1fr auto;
  }

  .market-indices {
    flex-direction: column;
    gap: 12px;
  }

  .index-item {
    padding: 12px;
  }

  .chart-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .key-metrics {
    grid-template-columns: repeat(3, 1fr);
  }
}
</style>
