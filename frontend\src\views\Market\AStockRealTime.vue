<template>
  <div class="market-realtime-container">
    <h1>A股实时行情</h1>
    <p>页面正在开发中...</p>

    <div class="test-content">
      <h2>测试数据</h2>
      <div>当前时间: {{ currentTime }}</div>
      <div>测试计数: {{ count }}</div>
      <button @click="count++">点击测试</button>
    </div>


  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

// 响应式数据
const count = ref(0)
const currentTime = computed(() => new Date().toLocaleString())






</script>

<style scoped>
.market-realtime-container {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

.test-content {
  margin-top: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

button {
  padding: 8px 16px;
  background: #409eff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 10px;
}

button:hover {
  background: #66b1ff;
}
</style>
