"""
统一缓存管理系统
整合所有缓存实现，提供统一接口，解决缓存策略冲突问题
支持Redis优先，内存缓存降级的策略
"""

import asyncio
import json
import logging
import pickle
from abc import ABC, abstractmethod
from datetime import datetime, timedelta
from enum import Enum
from typing import Any, Dict, List, Optional, Union

try:
    import redis.asyncio as redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False

from app.core.config import settings

logger = logging.getLogger(__name__)


class CacheLevel(Enum):
    """缓存级别"""
    L1_MEMORY = "memory"     # 内存缓存 (最快)
    L2_REDIS = "redis"       # Redis缓存 (共享)
    L3_DATABASE = "database" # 数据库缓存 (最慢，但持久)


class DataType(Enum):
    """数据类型分类"""
    REALTIME = "realtime"         # 实时数据 (30秒TTL)
    MARKET_QUOTE = "market_quote" # 行情报价 (2分钟TTL)  
    KLINE_DATA = "kline_data"     # K线数据 (15分钟TTL)
    STOCK_INFO = "stock_info"     # 股票基础信息 (1小时TTL)
    USER_SESSION = "user_session" # 用户会话 (30分钟TTL)
    HISTORICAL = "historical"     # 历史数据 (1天TTL)


class CacheConfig:
    """统一缓存配置"""
    
    # 默认TTL配置 (秒)
    DEFAULT_TTL = {
        DataType.REALTIME: 30,
        DataType.MARKET_QUOTE: 120,
        DataType.KLINE_DATA: 900,
        DataType.STOCK_INFO: 3600,
        DataType.USER_SESSION: 1800,
        DataType.HISTORICAL: 86400,
    }
    
    # 缓存键前缀
    KEY_PREFIXES = {
        DataType.REALTIME: "rt",
        DataType.MARKET_QUOTE: "quote", 
        DataType.KLINE_DATA: "kline",
        DataType.STOCK_INFO: "stock",
        DataType.USER_SESSION: "session",
        DataType.HISTORICAL: "hist",
    }
    
    # 内存缓存大小限制
    MEMORY_CACHE_SIZE = 10000  # 最大条目数
    
    @classmethod
    def get_ttl(cls, data_type: DataType, custom_ttl: Optional[int] = None) -> int:
        """获取TTL"""
        return custom_ttl if custom_ttl is not None else cls.DEFAULT_TTL.get(data_type, 300)
    
    @classmethod
    def build_key(cls, data_type: DataType, *parts: str) -> str:
        """构建标准化缓存键"""
        prefix = cls.KEY_PREFIXES.get(data_type, "cache")
        return ":".join([prefix] + list(parts))


class MemoryCache:
    """内存缓存实现 (LRU策略)"""
    
    def __init__(self, max_size: int = 10000):
        self.max_size = max_size
        self._cache: Dict[str, Dict[str, Any]] = {}
        self._access_order: List[str] = []
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        if key not in self._cache:
            return None
        
        entry = self._cache[key]
        
        # 检查过期时间
        if entry.get("expires_at") and datetime.now() > entry["expires_at"]:
            self.delete(key)
            return None
        
        # 更新访问顺序
        if key in self._access_order:
            self._access_order.remove(key)
        self._access_order.append(key)
        
        return entry["value"]
    
    def set(self, key: str, value: Any, expire: Optional[int] = None) -> bool:
        """设置缓存值"""
        try:
            # 计算过期时间
            expires_at = None
            if expire:
                expires_at = datetime.now() + timedelta(seconds=expire)
            
            # 如果缓存已满，删除最旧的条目
            if len(self._cache) >= self.max_size and key not in self._cache:
                if self._access_order:
                    oldest_key = self._access_order.pop(0)
                    del self._cache[oldest_key]
            
            self._cache[key] = {
                "value": value,
                "created_at": datetime.now(),
                "expires_at": expires_at
            }
            
            # 更新访问顺序
            if key in self._access_order:
                self._access_order.remove(key)
            self._access_order.append(key)
            
            return True
        except Exception as e:
            logger.error(f"Memory cache set error: {e}")
            return False
    
    def delete(self, key: str) -> bool:
        """删除缓存"""
        if key in self._cache:
            del self._cache[key]
        if key in self._access_order:
            self._access_order.remove(key)
        return True
    
    def clear(self) -> int:
        """清空缓存"""
        count = len(self._cache)
        self._cache.clear()
        self._access_order.clear()
        return count
    
    def stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        return {
            "size": len(self._cache),
            "max_size": self.max_size,
            "hit_rate": 0,  # 可以添加命中率统计
        }


class RedisCache:
    """Redis缓存实现"""
    
    def __init__(self):
        self.client: Optional[redis.Redis] = None
        self._connected = False
    
    async def connect(self):
        """连接Redis"""
        if not REDIS_AVAILABLE or self._connected:
            return
        
        try:
            self.client = redis.Redis(
                host=getattr(settings, "REDIS_HOST", "localhost"),
                port=getattr(settings, "REDIS_PORT", 6379),
                db=getattr(settings, "REDIS_DB", 0),
                decode_responses=False,
                socket_timeout=5,
                socket_connect_timeout=5
            )
            
            await self.client.ping()
            self._connected = True
            logger.info("Redis cache connected")
            
        except Exception as e:
            logger.warning(f"Redis connection failed: {e}")
            self.client = None
    
    async def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        if not self.client:
            return None
        
        try:
            data = await self.client.get(key)
            if data is None:
                return None
            
            # 尝试JSON解析，失败则使用pickle
            try:
                return json.loads(data.decode('utf-8'))
            except (json.JSONDecodeError, UnicodeDecodeError):
                return pickle.loads(data)
                
        except Exception as e:
            logger.error(f"Redis get error: {e}")
            return None
    
    async def set(self, key: str, value: Any, expire: Optional[int] = None) -> bool:
        """设置缓存值"""
        if not self.client:
            return False
        
        try:
            # 序列化数据
            if isinstance(value, (dict, list, str, int, float, bool)):
                data = json.dumps(value, default=str).encode('utf-8')
            else:
                data = pickle.dumps(value)
            
            if expire:
                await self.client.setex(key, expire, data)
            else:
                await self.client.set(key, data)
            
            return True
            
        except Exception as e:
            logger.error(f"Redis set error: {e}")
            return False
    
    async def delete(self, key: str) -> bool:
        """删除缓存"""
        if not self.client:
            return False
        
        try:
            await self.client.delete(key)
            return True
        except Exception as e:
            logger.error(f"Redis delete error: {e}")
            return False
    
    async def clear(self, pattern: str = "*") -> int:
        """清空匹配的键"""
        if not self.client:
            return 0
        
        try:
            keys = await self.client.keys(pattern)
            if keys:
                return await self.client.delete(*keys)
            return 0
        except Exception as e:
            logger.error(f"Redis clear error: {e}")
            return 0
    
    async def close(self):
        """关闭连接"""
        if self.client:
            await self.client.close()
            self._connected = False


class UnifiedCacheManager:
    """统一缓存管理器"""
    
    def __init__(self):
        self.memory_cache = MemoryCache(CacheConfig.MEMORY_CACHE_SIZE)
        self.redis_cache = RedisCache()
        self._initialized = False
    
    async def initialize(self):
        """初始化缓存系统"""
        if self._initialized:
            return
        
        await self.redis_cache.connect()
        self._initialized = True
        logger.info("Unified cache manager initialized")
    
    async def get(self, data_type: DataType, *key_parts: str) -> Optional[Any]:
        """获取缓存值 (L1 -> L2 策略)"""
        key = CacheConfig.build_key(data_type, *key_parts)
        
        # L1: 内存缓存
        value = self.memory_cache.get(key)
        if value is not None:
            return value
        
        # L2: Redis缓存
        value = await self.redis_cache.get(key)
        if value is not None:
            # 回填到L1缓存
            ttl = CacheConfig.get_ttl(data_type)
            self.memory_cache.set(key, value, expire=min(ttl, 300))  # 内存缓存最多5分钟
            return value
        
        return None
    
    async def set(
        self, 
        data_type: DataType, 
        value: Any, 
        *key_parts: str,
        expire: Optional[int] = None
    ) -> bool:
        """设置缓存值 (同时设置L1和L2)"""
        key = CacheConfig.build_key(data_type, *key_parts)
        ttl = CacheConfig.get_ttl(data_type, expire)
        
        # 同时设置L1和L2缓存
        memory_success = self.memory_cache.set(key, value, expire=min(ttl, 300))
        redis_success = await self.redis_cache.set(key, value, expire=ttl)
        
        return memory_success or redis_success
    
    async def delete(self, data_type: DataType, *key_parts: str) -> bool:
        """删除缓存 (同时删除L1和L2)"""
        key = CacheConfig.build_key(data_type, *key_parts)
        
        memory_success = self.memory_cache.delete(key)
        redis_success = await self.redis_cache.delete(key)
        
        return memory_success or redis_success
    
    async def clear_type(self, data_type: DataType) -> int:
        """清空指定类型的缓存"""
        prefix = CacheConfig.KEY_PREFIXES.get(data_type, "cache")
        pattern = f"{prefix}:*"
        
        # 清空Redis中匹配的键
        redis_count = await self.redis_cache.clear(pattern)
        
        # 清空内存缓存中匹配的键
        memory_keys = [k for k in self.memory_cache._cache.keys() if k.startswith(f"{prefix}:")]
        memory_count = 0
        for key in memory_keys:
            if self.memory_cache.delete(key):
                memory_count += 1
        
        logger.info(f"Cleared {redis_count} Redis keys and {memory_count} memory keys for type {data_type}")
        return redis_count + memory_count
    
    async def clear_all(self) -> int:
        """清空所有缓存"""
        memory_count = self.memory_cache.clear()
        redis_count = await self.redis_cache.clear()
        
        logger.info(f"Cleared all cache: {redis_count} Redis keys, {memory_count} memory keys")
        return redis_count + memory_count
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        return {
            "memory_cache": self.memory_cache.stats(),
            "redis_connected": self.redis_cache._connected,
            "initialized": self._initialized
        }
    
    async def close(self):
        """关闭缓存管理器"""
        await self.redis_cache.close()
        self.memory_cache.clear()
        logger.info("Unified cache manager closed")


# 全局统一缓存管理器实例
unified_cache_manager = UnifiedCacheManager()


# 便捷函数
async def get_cache(data_type: DataType, *key_parts: str) -> Optional[Any]:
    """获取缓存"""
    return await unified_cache_manager.get(data_type, *key_parts)


async def set_cache(
    data_type: DataType, 
    value: Any, 
    *key_parts: str,
    expire: Optional[int] = None
) -> bool:
    """设置缓存"""
    return await unified_cache_manager.set(data_type, value, *key_parts, expire=expire)


async def delete_cache(data_type: DataType, *key_parts: str) -> bool:
    """删除缓存"""
    return await unified_cache_manager.delete(data_type, *key_parts)


async def clear_cache_by_type(data_type: DataType) -> int:
    """按类型清空缓存"""
    return await unified_cache_manager.clear_type(data_type)


# 数据源相关的缓存函数
async def cache_market_data(symbol: str, data: Dict[str, Any]) -> bool:
    """缓存市场数据"""
    return await set_cache(DataType.REALTIME, data, "market", symbol)


async def get_cached_market_data(symbol: str) -> Optional[Dict[str, Any]]:
    """获取缓存的市场数据"""
    return await get_cache(DataType.REALTIME, "market", symbol)


async def cache_kline_data(symbol: str, interval: str, data: List[Dict[str, Any]]) -> bool:
    """缓存K线数据"""
    return await set_cache(DataType.KLINE_DATA, data, symbol, interval)


async def get_cached_kline_data(symbol: str, interval: str) -> Optional[List[Dict[str, Any]]]:
    """获取缓存的K线数据"""
    return await get_cache(DataType.KLINE_DATA, symbol, interval)


async def cache_stock_info(symbol: str, data: Dict[str, Any]) -> bool:
    """缓存股票信息"""
    return await set_cache(DataType.STOCK_INFO, data, symbol)


async def get_cached_stock_info(symbol: str) -> Optional[Dict[str, Any]]:
    """获取缓存的股票信息"""
    return await get_cache(DataType.STOCK_INFO, symbol)